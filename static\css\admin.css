/* Admin-specific styles */

/* Dashboard cards */
.admin-dashboard-card {
    border-left: 4px solid;
    border-radius: 4px;
    transition: transform 0.2s ease;
}

.admin-dashboard-card:hover {
    transform: translateY(-5px);
}

.admin-dashboard-card.primary {
    border-left-color: #0d6efd;
}

.admin-dashboard-card.success {
    border-left-color: #198754;
}

.admin-dashboard-card.warning {
    border-left-color: #ffc107;
}

.admin-dashboard-card.danger {
    border-left-color: #dc3545;
}

.admin-dashboard-card .card-icon {
    font-size: 2.5rem;
    opacity: 0.8;
}

/* Table styling */
.table th {
    background-color: #f8f9fa;
}

/* Status badges */
.badge {
    font-size: 0.8rem;
    padding: 0.35em 0.65em;
}

/* Form controls */
.form-control:focus, .form-select:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* Admin navbar customization */
.navbar-dark .navbar-nav .nav-link {
    color: rgba(255, 255, 255, 0.8);
}

.navbar-dark .navbar-nav .nav-link:hover {
    color: #fff;
}

/* Admin sidebar (for larger screens) */
@media (min-width: 992px) {
    .admin-sidebar {
        min-height: calc(100vh - 56px);
        background-color: #343a40;
        padding-top: 1rem;
    }
    
    .admin-sidebar .nav-link {
        color: rgba(255, 255, 255, 0.8);
        padding: 0.75rem 1rem;
        border-left: 3px solid transparent;
    }
    
    .admin-sidebar .nav-link:hover,
    .admin-sidebar .nav-link.active {
        color: #fff;
        background-color: rgba(255, 255, 255, 0.1);
        border-left-color: #0d6efd;
    }
    
    .admin-sidebar .nav-link i {
        margin-right: 0.5rem;
    }
}

/* Order status colors */
.status-pending {
    color: #ffc107;
}

.status-processing {
    color: #0dcaf0;
}

.status-shipped {
    color: #0d6efd;
}

.status-delivered {
    color: #198754;
}

.status-cancelled {
    color: #dc3545;
}

/* Image preview */
.img-preview {
    max-width: 100px;
    max-height: 100px;
    object-fit: contain;
}