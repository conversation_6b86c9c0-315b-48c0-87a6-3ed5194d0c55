{% extends "admin_base.html" %}
{% block title %}Manage Inventory{% endblock %}
{% block content %}
<div class="container py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1><i class="fas fa-boxes"></i> Manage Inventory</h1>
        <a href="{{ url_for('admin_add_item') }}" class="btn btn-success">
            <i class="fas fa-plus"></i> Add New Item
        </a>
    </div>

    <div class="card">
        <div class="card-header">
            <h3>Current Inventory</h3>
        </div>
        <div class="card-body">
            {% if items and items|length > 0 %}
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Image</th>
                            <th>Name</th>
                            <th>Category</th>
                            <th>Price</th>
                            <th>Quantity</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for item in items %}
                        <tr>
                            <td>
                                <img src="{{ url_for('static', filename='img/' + item.get('image', 'default.jpg')) }}" 
                                     alt="{{ item.name }}" class="img-thumbnail" style="max-width: 50px;">
                            </td>
                            <td>{{ item.get('name', '') }}</td>
                            <td>{{ item.get('category', 'Uncategorized') }}</td>
                            <td>${{ "%.2f"|format(item.get('price', 0)) }}</td>
                            <td>
                                <form method="POST" action="{{ url_for('admin_update_item', item_name=item.get('name', '')) }}" class="d-flex">
                                    <input type="number" name="quantity" value="{{ item.get('quantity', 0) }}" 
                                           class="form-control form-control-sm" style="width: 80px;" min="0" required>
                                    <input type="number" name="price" value="{{ item.get('price', 0) }}" 
                                           class="form-control form-control-sm ms-2" style="width: 80px;" min="0.01" step="0.01" required>
                                    <button type="submit" class="btn btn-sm btn-primary ms-2">Update</button>
                                </form>
                            </td>
                            <td>
                                {% if item.get('in_stock', False) %}
                                <span class="badge bg-success">In Stock</span>
                                {% else %}
                                <span class="badge bg-danger">Out of Stock</span>
                                {% endif %}
                            </td>
                            <td>
                                <a href="{{ url_for('admin_edit_item', item_name=item.get('name', '')) }}" class="btn btn-sm btn-info">
                                    <i class="fas fa-edit"></i> Edit
                                </a>
                                <a href="{{ url_for('admin_delete_item', item_name=item.get('name', '')) }}" 
                                   class="btn btn-sm btn-danger"
                                   onclick="return confirm('Are you sure you want to delete this item?');">
                                    <i class="fas fa-trash"></i> Delete
                                </a>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i> No items in inventory. Click "Add New Item" to add products.
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}



