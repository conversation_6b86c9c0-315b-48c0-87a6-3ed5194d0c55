{% extends "admin_base.html" %}
{% block title %}Edit Item{% endblock %}
{% block content %}
<div class="container py-4">
    <div class="row">
        <div class="col-md-8 mx-auto">
            <div class="card">
                <div class="card-header">
                    <h3><i class="fas fa-edit"></i> Edit Item: {{ item.name }}</h3>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ url_for('admin_edit_item', item_name=item.name) }}">
                        <div class="mb-3 text-center">
                            <img src="{{ url_for('static', filename='img/' + item.get('image', 'default.jpg')) }}" 
                                 alt="{{ item.get('name', '') }}" class="img-thumbnail" style="max-height: 150px;">
                        </div>
                        
                        <div class="mb-3">
                            <label for="name" class="form-label">Item Name</label>
                            <input type="text" class="form-control" id="name" value="{{ item.get('name', '') }}" disabled>
                            <div class="form-text">Item name cannot be changed. Create a new item instead.</div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="price" class="form-label">Price ($)</label>
                                <input type="number" class="form-control" id="price" name="price" 
                                       min="0.01" step="0.01" value="{{ item.get('price', 0) }}" required>
                            </div>
                            <div class="col-md-6">
                                <label for="quantity" class="form-label">Quantity</label>
                                <input type="number" class="form-control" id="quantity" name="quantity" 
                                       min="0" value="{{ item.get('quantity', 0) }}" required>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="category" class="form-label">Category</label>
                            <select class="form-select" id="category" name="category">
                                <option value="Fruits" {% if item.get('category') == 'Fruits' %}selected{% endif %}>Fruits</option>
                                <option value="Vegetables" {% if item.get('category') == 'Vegetables' %}selected{% endif %}>Vegetables</option>
                                <option value="Dairy" {% if item.get('category') == 'Dairy' %}selected{% endif %}>Dairy</option>
                                <option value="Bakery" {% if item.get('category') == 'Bakery' %}selected{% endif %}>Bakery</option>
                                <option value="Meat" {% if item.get('category') == 'Meat' %}selected{% endif %}>Meat</option>
                                <option value="Beverages" {% if item.get('category') == 'Beverages' %}selected{% endif %}>Beverages</option>
                                <option value="Snacks" {% if item.get('category') == 'Snacks' %}selected{% endif %}>Snacks</option>
                                <option value="Canned Goods" {% if item.get('category') == 'Canned Goods' %}selected{% endif %}>Canned Goods</option>
                                <option value="Frozen Foods" {% if item.get('category') == 'Frozen Foods' %}selected{% endif %}>Frozen Foods</option>
                                <option value="Household" {% if item.get('category') == 'Household' %}selected{% endif %}>Household</option>
                                <option value="Other" {% if item.get('category') == 'Other' %}selected{% endif %}>Other</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label for="description" class="form-label">Description</label>
                            <textarea class="form-control" id="description" name="description" rows="3">{{ item.get('description', '') }}</textarea>
                        </div>
                        
                        <div class="mb-3">
                            <label for="image" class="form-label">Image Filename</label>
                            <input type="text" class="form-control" id="image" name="image" 
                                   value="{{ item.get('image', 'default.jpg') }}" placeholder="e.g., apple.jpg">
                            <div class="form-text">Enter the filename of an image in the static/img directory</div>
                        </div>
                        
                        <div class="d-flex justify-content-between">
                            <a href="{{ url_for('admin_inventory') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Back to Inventory
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Save Changes
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}


