import os
import json
import uuid
import logging
from datetime import datetime
from flask import Flask, render_template, request, redirect, url_for, session, flash
from werkzeug.exceptions import HTTPException
from functools import wraps

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('app.log'),
        logging.StreamHandler()
    ]
)

app = Flask(__name__)
app.secret_key = os.environ.get('SECRET_KEY', 'dev_key_for_testing_only')
app.config['SESSION_TYPE'] = 'filesystem'
app.config['PERMANENT_SESSION_LIFETIME'] = 1800  # 30 minutes

# Helper functions
def load_grocery_data():
    try:
        if os.path.exists('grocery_data.json'):
            with open('grocery_data.json', 'r') as f:
                data = json.load(f)
                if not isinstance(data, list):
                    app.logger.error(f"grocery_data.json did not contain a list: {type(data)}")
                    return []
                return data
        return []
    except json.JSONDecodeError:
        app.logger.error("Error decoding grocery_data.json")
        return []
    except Exception as e:
        app.logger.error(f"Unexpected error loading grocery data: {str(e)}")
        return []

def save_grocery_data(items):
    try:
        if not isinstance(items, list):
            app.logger.error(f"Cannot save grocery data: not a list: {type(items)}")
            items = []
            
        with open('grocery_data.json', 'w') as f:
            json.dump(items, f, indent=4)
    except Exception as e:
        app.logger.error(f"Error saving grocery data: {str(e)}")

def load_orders():
    try:
        if os.path.exists('orders.json'):
            with open('orders.json', 'r') as f:
                data = json.load(f)
                if not isinstance(data, list):
                    app.logger.error(f"orders.json did not contain a list: {type(data)}")
                    return []
                return data
        return []
    except json.JSONDecodeError:
        app.logger.error("Error decoding orders.json")
        return []
    except Exception as e:
        app.logger.error(f"Unexpected error loading orders: {str(e)}")
        return []

def save_orders(orders):
    try:
        if not isinstance(orders, list):
            app.logger.error(f"Cannot save orders: not a list: {type(orders)}")
            orders = []
            
        with open('orders.json', 'w') as f:
            json.dump(orders, f, indent=4)
    except Exception as e:
        app.logger.error(f"Error saving orders: {str(e)}")

def load_users():
    try:
        if os.path.exists('users.json'):
            with open('users.json', 'r') as f:
                data = json.load(f)
                if not isinstance(data, list):
                    app.logger.error(f"users.json did not contain a list: {type(data)}")
                    return []
                return data
        return []
    except json.JSONDecodeError:
        app.logger.error("Error decoding users.json")
        return []
    except Exception as e:
        app.logger.error(f"Unexpected error loading users: {str(e)}")
        return []

def save_users(users):
    try:
        if not isinstance(users, list):
            app.logger.error(f"Cannot save users: not a list: {type(users)}")
            users = []
            
        with open('users.json', 'w') as f:
            json.dump(users, f, indent=4)
    except Exception as e:
        app.logger.error(f"Error saving users: {str(e)}")

# Admin authentication decorator
def admin_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not session.get('admin_logged_in'):
            flash("Please login first!")
            return redirect(url_for('admin_login'))
        return f(*args, **kwargs)
    return decorated_function

# Routes
@app.route('/')
@app.route('/home')
def home():
    try:
        # Load all grocery items
        items = load_grocery_data()
        
        # Debug log to see what items we have
        app.logger.debug(f"Loaded {len(items)} items from grocery data")
        
        # Select up to 4 items as featured products
        featured_items = []
        if items and len(items) > 0:
            # Make sure all items have the required fields
            for item in items:
                if 'image' not in item:
                    item['image'] = 'default.jpg'
                if 'in_stock' not in item:
                    item['in_stock'] = item.get('quantity', 0) > 0
            
            # Get items that are in stock
            valid_items = [item for item in items if item.get('in_stock', False)]
            
            # If we have valid items, select up to 4 random ones
            if valid_items:
                import random
                featured_items = random.sample(valid_items, min(4, len(valid_items)))
                app.logger.debug(f"Selected {len(featured_items)} featured items")
            else:
                # If no valid items, just use the first few items
                featured_items = items[:min(4, len(items))]
                app.logger.debug("No valid items found, using first few items")
        
        return render_template('home.html', featured_items=featured_items)
    except Exception as e:
        app.logger.error(f"Error in home route: {str(e)}")
        flash(f"Error loading homepage: {str(e)}")
        return render_template('home.html', featured_items=[])

@app.route('/shop')
def shop():
    try:
        items = load_grocery_data()
        
        # Filter by category if specified
        category = request.args.get('category')
        if category:
            items = [item for item in items if item.get('category', 'Uncategorized') == category]
        
        # Filter by price if specified
        min_price = request.args.get('min_price')
        max_price = request.args.get('max_price')
        
        if min_price:
            try:
                min_price = float(min_price)
                items = [item for item in items if item.get('price', 0) >= min_price]
            except ValueError:
                pass
                
        if max_price:
            try:
                max_price = float(max_price)
                items = [item for item in items if item.get('price', 0) <= max_price]
            except ValueError:
                pass
        
        # Get all categories for the sidebar
        categories = sorted(set(item.get('category', 'Uncategorized') for item in load_grocery_data()))
        
        return render_template('shop.html', items=items, categories=categories)
    except Exception as e:
        app.logger.error(f"Error in shop route: {str(e)}")
        flash(f"Error loading shop: {str(e)}")
        return redirect(url_for('home'))

@app.route('/add_to_cart/<item_name>', methods=['POST'])
def add_to_cart(item_name):
    try:
        items = load_grocery_data()
        item = next((i for i in items if i['name'] == item_name), None)
        
        if not item:
            flash(f"Item '{item_name}' not found!")
            return redirect(url_for('shop'))
        
        quantity = int(request.form.get('quantity', 1))
        if quantity <= 0:
            flash("Quantity must be positive!")
            return redirect(url_for('shop'))
        
        if quantity > item['quantity']:
            flash(f"Sorry, only {item['quantity']} units of {item_name} are available.")
            return redirect(url_for('shop'))
        
        cart = session.get('cart', [])
        
        # Check if item already in cart
        cart_item = next((i for i in cart if i['name'] == item_name), None)
        if cart_item:
            cart_item['quantity'] += quantity
        else:
            cart.append({
                'name': item['name'],
                'price': item['price'],
                'quantity': quantity,
                'image': item.get('image', 'default.jpg')
            })
        
        session['cart'] = cart
        flash(f"Added {quantity} {item_name} to cart!")
        return redirect(url_for('shop'))
    except ValueError:
        flash("Please enter a valid quantity!")
        return redirect(url_for('shop'))
    except Exception as e:
        app.logger.error(f"Error in add_to_cart: {str(e)}")
        flash(f"Error adding to cart: {str(e)}")
        return redirect(url_for('shop'))

@app.route('/cart')
def view_cart():
    try:
        cart = session.get('cart', [])
        total = sum(item.get('price', 0) * item.get('quantity', 0) for item in cart)
        return render_template('cart.html', cart=cart, total=total)
    except Exception as e:
        app.logger.error(f"Error in view_cart: {str(e)}")
        flash(f"Error viewing cart: {str(e)}")
        return redirect(url_for('shop'))

@app.route('/update_cart/<item_name>', methods=['POST'])
def update_cart(item_name):
    try:
        cart = session.get('cart', [])
        quantity = int(request.form.get('quantity', 0))
        
        if quantity <= 0:
            # Remove item from cart
            cart = [item for item in cart if item['name'] != item_name]
            flash(f"Removed {item_name} from cart!")
        else:
            # Update quantity
            cart_item = next((i for i in cart if i['name'] == item_name), None)
            if cart_item:
                # Check if requested quantity is available
                items = load_grocery_data()
                stock_item = next((i for i in items if i['name'] == item_name), None)
                
                if stock_item and quantity > stock_item['quantity']:
                    flash(f"Sorry, only {stock_item['quantity']} units of {item_name} are available.")
                    quantity = stock_item['quantity']
                
                cart_item['quantity'] = quantity
                flash(f"Updated {item_name} quantity to {quantity}!")
        
        session['cart'] = cart
        return redirect(url_for('view_cart'))
    except ValueError:
        flash("Please enter a valid quantity!")
        return redirect(url_for('view_cart'))
    except Exception as e:
        app.logger.error(f"Error in update_cart: {str(e)}")
        flash(f"Error updating cart: {str(e)}")
        return redirect(url_for('view_cart'))

@app.route('/remove_from_cart/<item_name>')
def remove_from_cart(item_name):
    try:
        cart = session.get('cart', [])
        cart = [item for item in cart if item['name'] != item_name]
        session['cart'] = cart
        flash(f"Removed {item_name} from cart!")
        return redirect(url_for('view_cart'))
    except Exception as e:
        app.logger.error(f"Error in remove_from_cart: {str(e)}")
        flash(f"Error removing from cart: {str(e)}")
        return redirect(url_for('view_cart'))

@app.route('/checkout', methods=['GET', 'POST'])
def checkout():
    if 'cart' not in session or not session['cart']:
        flash("Your cart is empty!")
        return redirect(url_for('shop'))
    
    if request.method == 'POST':
        try:
            # Get form data
            name = request.form.get('name')
            email = request.form.get('email')
            phone = request.form.get('phone')
            address = request.form.get('address')
            
            # Validate form data
            if not all([name, email, phone, address]):
                flash("Please fill out all fields!")
                return redirect(url_for('checkout'))
            
            # Create order
            cart = session.get('cart', [])
            items = []
            total = 0
            
            # Get grocery data to get item details
            grocery_data = load_grocery_data()
            
            # Process each item in cart
            for cart_item in cart:
                item_name = cart_item.get('name')
                quantity = cart_item.get('quantity', 0)
                
                # Find item in grocery data
                item_data = next((item for item in grocery_data if item.get('name') == item_name), None)
                
                if item_data:
                    price = item_data.get('price', 0)
                    items.append({
                        'name': item_name,
                        'price': price,
                        'quantity': quantity
                    })
                    total += price * quantity
            
            # Create order object
            import datetime
            import uuid
            
            order = {
                'order_id': str(uuid.uuid4())[:8],
                'date': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'name': name,
                'email': email,
                'phone': phone,
                'address': address,
                'items': items,
                'total': total
            }
            
            # Save order to orders.json
            orders = []
            try:
                if os.path.exists('orders.json'):
                    with open('orders.json', 'r') as f:
                        orders = json.load(f)
            except Exception as e:
                app.logger.error(f"Error loading orders: {str(e)}")
            
            orders.append(order)
            
            try:
                with open('orders.json', 'w') as f:
                    json.dump(orders, f, indent=4)
            except Exception as e:
                app.logger.error(f"Error saving order: {str(e)}")
                flash("There was an error processing your order. Please try again.")
                return redirect(url_for('checkout'))
            
            # Clear cart
            session['cart'] = []
            
            # Redirect to confirmation page
            return render_template('order_confirmation.html', order=order)
            
        except Exception as e:
            app.logger.error(f"Error in checkout: {str(e)}")
            flash(f"Error processing checkout: {str(e)}")
            return redirect(url_for('checkout'))
    
    # GET request - show checkout form
    cart = session.get('cart', [])
    total = sum(item.get('price', 0) * item.get('quantity', 0) for item in cart)
    
    return render_template('checkout.html', cart=cart, total=total)

@app.route('/order_confirmation/<order_id>')
def order_confirmation(order_id):
    try:
        orders = load_orders()
        order = next((o for o in orders if o.get('order_id') == order_id), None)
        
        if not order:
            flash("Order not found!")
            return redirect(url_for('shop'))
        
        return render_template('order_confirmation.html', order=order)
    except Exception as e:
        app.logger.error(f"Error in order_confirmation: {str(e)}")
        flash(f"Error displaying order: {str(e)}")
        return redirect(url_for('shop'))

@app.route('/admin')
def admin_redirect():
    return redirect(url_for('admin_login'))

@app.route('/admin/login', methods=['GET', 'POST'])
def admin_login():
    try:
        if request.method == 'POST':
            username = request.form.get('username')
            password = request.form.get('password')
            
            if username == 'admin' and password == 'admin123':
                session['admin_logged_in'] = True
                flash("Welcome, Admin!")
                return redirect(url_for('admin_dashboard'))
            else:
                flash("Invalid credentials!")
        
        return render_template('admin_login.html')
    except Exception as e:
        app.logger.error(f"Error in admin_login: {str(e)}")
        flash(f"Error during login: {str(e)}")
        return render_template('admin_login.html')

@app.route('/admin/logout')
def admin_logout():
    session.pop('admin_logged_in', None)
    flash("You have been logged out.")
    return redirect(url_for('admin_login'))

@app.route('/admin/dashboard')
@admin_required
def admin_dashboard():
    try:
        orders = load_orders()
        
        # Sort orders by date (newest first)
        if orders:
            try:
                orders.sort(key=lambda x: x.get('date', ''), reverse=True)
            except Exception as e:
                app.logger.error(f"Error sorting orders: {str(e)}")
        
        # Count orders by status
        pending_count = sum(1 for o in orders if o.get('status') == 'pending')
        confirmed_count = sum(1 for o in orders if o.get('status') == 'confirmed')
        delivered_count = sum(1 for o in orders if o.get('status') == 'delivered')
        
        return render_template(
            'admin_dashboard.html', 
            orders=orders, 
            pending_count=pending_count,
            confirmed_count=confirmed_count,
            delivered_count=delivered_count
        )
    except Exception as e:
        app.logger.error(f"Error in admin_dashboard: {str(e)}")
        flash(f"Dashboard error: {str(e)}")
        return redirect(url_for('admin_login'))

@app.route('/admin/update_order/<order_id>', methods=['POST'])
@admin_required
def update_order_status(order_id):
    try:
        status = request.form.get('status')
        if not status:
            flash("No status provided!")
            return redirect(url_for('admin_dashboard'))
        
        orders = load_orders()
        order = next((o for o in orders if o.get('order_id') == order_id), None)
        
        if not order:
            flash("Order not found!")
            return redirect(url_for('admin_dashboard'))
        
        order['status'] = status
        save_orders(orders)
        
        flash(f"Order {order_id[:8]}... updated to {status}!")
        return redirect(url_for('admin_dashboard'))
    except Exception as e:
        app.logger.error(f"Error in update_order_status: {str(e)}")
        flash(f"Error updating order: {str(e)}")
        return redirect(url_for('admin_dashboard'))

@app.route('/admin/inventory')
@admin_required
def admin_inventory():
    try:
        items = load_grocery_data()
        
        # Ensure all items have the required fields
        for item in items:
            if 'image' not in item:
                item['image'] = 'default.jpg'
            if 'in_stock' not in item:
                item['in_stock'] = item.get('quantity', 0) > 0
                
        return render_template('admin_inventory.html', items=items)
    except Exception as e:
        app.logger.error(f"Error in admin_inventory: {str(e)}")
        flash(f"Error loading inventory: {str(e)}", "danger")
        return redirect(url_for('admin_dashboard'))

@app.route('/admin/add_item', methods=['GET', 'POST'])
@admin_required
def admin_add_item():
    try:
        if request.method == 'POST':
            name = request.form.get('name')
            price = float(request.form.get('price', 0))
            quantity = int(request.form.get('quantity', 0))
            category = request.form.get('category')
            description = request.form.get('description', '')
            image = request.form.get('image', 'default.jpg')
            
            if not name or price <= 0 or quantity < 0:
                flash("Please provide valid item details!")
                return render_template('admin_add_item.html')
            
            items = load_grocery_data()
            
            # Check if item already exists
            if any(i['name'] == name for i in items):
                flash(f"Item '{name}' already exists!")
                return render_template('admin_add_item.html')
            
            # Add new item
            items.append({
                'name': name,
                'price': price,
                'quantity': quantity,
                'category': category,
                'description': description,
                'image': image,
                'in_stock': quantity > 0
            })
            
            save_grocery_data(items)
            flash(f"Item '{name}' added successfully!")
            return redirect(url_for('admin_inventory'))
        
        return render_template('admin_add_item.html')
    except ValueError:
        flash("Please enter valid numbers for price and quantity!")
        return render_template('admin_add_item.html')
    except Exception as e:
        app.logger.error(f"Error in admin_add_item: {str(e)}")
        flash(f"Error adding item: {str(e)}")
        return redirect(url_for('admin_inventory'))

@app.route('/admin/edit_item/<item_name>', methods=['GET', 'POST'])
@admin_required
def admin_edit_item(item_name):
    try:
        items = load_grocery_data()
        item = next((i for i in items if i.get('name') == item_name), None)
        
        if not item:
            flash(f"Item '{item_name}' not found!")
            return redirect(url_for('admin_inventory'))
        
        if request.method == 'POST':
            price = float(request.form.get('price', 0))
            quantity = int(request.form.get('quantity', 0))
            category = request.form.get('category')
            description = request.form.get('description', '')
            image = request.form.get('image', 'default.jpg')
            
            if price <= 0 or quantity < 0:
                flash("Please provide valid item details!")
                return render_template('admin_edit_item.html', item=item)
            
            # Update item
            item['price'] = price
            item['quantity'] = quantity
            item['category'] = category
            item['description'] = description
            item['image'] = image
            item['in_stock'] = quantity > 0
            
            save_grocery_data(items)
            flash(f"Item '{item_name}' updated successfully!")
            return redirect(url_for('admin_inventory'))
        
        return render_template('admin_edit_item.html', item=item)
    except ValueError:
        flash("Please enter valid numbers for price and quantity!")
        return render_template('admin_edit_item.html', item=item)
    except Exception as e:
        app.logger.error(f"Error in admin_edit_item: {str(e)}")
        flash(f"Error editing item: {str(e)}")
        return redirect(url_for('admin_inventory'))

@app.route('/admin/delete_item/<item_name>')
@admin_required
def admin_delete_item(item_name):
    try:
        items = load_grocery_data()
        items = [i for i in items if i['name'] != item_name]
        save_grocery_data(items)
        flash(f"Item '{item_name}' deleted successfully!")
        return redirect(url_for('admin_inventory'))
    except Exception as e:
        app.logger.error(f"Error in admin_delete_item: {str(e)}")
        flash(f"Error deleting item: {str(e)}")
        return redirect(url_for('admin_inventory'))

@app.route('/admin/update_item/<item_name>', methods=['POST'])
@admin_required
def admin_update_item(item_name):
    try:
        items = load_grocery_data()
        item = next((i for i in items if i['name'] == item_name), None)
        
        if not item:
            flash(f"Item '{item_name}' not found!")
            return redirect(url_for('admin_inventory'))
        
        quantity = int(request.form.get('quantity', 0))
        price = float(request.form.get('price', 0))
        
        if quantity < 0 or price <= 0:
            flash("Please provide valid values!")
            return redirect(url_for('admin_inventory'))
        
        # Update item
        item['quantity'] = quantity
        item['price'] = price
        item['in_stock'] = quantity > 0
        
        save_grocery_data(items)
        flash(f"Item '{item_name}' updated successfully!")
        return redirect(url_for('admin_inventory'))
    except ValueError:
        flash("Please enter valid numbers for price and quantity!")
        return redirect(url_for('admin_inventory'))
    except Exception as e:
        app.logger.error(f"Error in admin_update_item: {str(e)}")
        flash(f"Error updating item: {str(e)}")
        return redirect(url_for('admin_inventory'))

# Debug routes
@app.route('/debug/orders')
def debug_orders():
    if not app.debug:
        return "Debug mode is disabled", 403
    
    try:
        orders = load_orders()
        return render_template('debug_orders.html', orders=orders)
    except Exception as e:
        app.logger.error(f"Error in debug_orders: {str(e)}")
        flash(f"Error debugging orders: {str(e)}")
        return redirect(url_for('home'))

@app.route('/debug/clear_orders')
def debug_clear_orders():
    if not app.debug:
        return "Debug mode is disabled", 403
    
    try:
        save_orders([])
        flash("Orders cleared for debugging")
        return redirect(url_for('debug_orders'))
    except Exception as e:
        app.logger.error(f"Error in debug_clear_orders: {str(e)}")
        flash(f"Error clearing orders: {str(e)}")
        return redirect(url_for('debug_orders'))

@app.route('/debug/fix_orders')
def debug_fix_orders():
    if not app.debug:
        return "Debug mode is disabled", 403
    
    try:
        # Create a new empty orders file
        save_orders([])
        flash("Orders file has been reset")
        return redirect(url_for('admin_dashboard'))
    except Exception as e:
        app.logger.error(f"Error in debug_fix_orders: {str(e)}")
        flash(f"Error fixing orders: {str(e)}")
        return redirect(url_for('admin_login'))

# Error handlers
@app.errorhandler(404)
def page_not_found(e):
    return render_template('error.html', error=e, title="Page Not Found"), 404

@app.errorhandler(500)
def server_error(e):
    return render_template('error.html', error=e, title="Server Error"), 500

@app.errorhandler(Exception)
def handle_exception(e):
    # Pass through HTTP errors
    if isinstance(e, HTTPException):
        return e

    # Now you're handling non-HTTP exceptions only
    app.logger.error(f"Unhandled exception: {str(e)}")
    return render_template('error.html', 
                          error="An unexpected error occurred. Please try again later.", 
                          title="Unexpected Error"), 500

if __name__ == '__main__':
    # Create data files if they don't exist
    if not os.path.exists('grocery_data.json'):
        sample_items = [
            {
                "name": "Apples",
                "price": 1.99,
                "quantity": 50,
                "category": "Fruits",
                "description": "Fresh red apples",
                "image": "apple.jpg",
                "in_stock": True
            },
            {
                "name": "Bananas",
                "price": 0.99,
                "quantity": 40,
                "category": "Fruits",
                "description": "Ripe yellow bananas",
                "image": "banana.jpg",
                "in_stock": True
            },
            {
                "name": "Milk",
                "price": 2.49,
                "quantity": 20,
                "category": "Dairy",
                "description": "Fresh whole milk",
                "image": "milk.jpg",
                "in_stock": True
            }
        ]
        save_grocery_data(sample_items)
    
    if not os.path.exists('orders.json'):
        save_orders([])
    
    if not os.path.exists('users.json'):
        save_users([])
    
    app.run(debug=True)
