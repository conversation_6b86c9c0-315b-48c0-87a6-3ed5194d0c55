{% extends "base.html" %}
{% block title %}Shop{% endblock %}
{% block content %}
<div class="container py-4">
    <div class="row">
        <!-- Sidebar with Categories -->
        <div class="col-lg-3 mb-4">
            <div class="sidebar">
                <div class="sidebar-header">
                    <h3>Categories</h3>
                </div>
                <div class="sidebar-body">
                    <ul class="category-list">
                        <li class="category-item {% if not request.args.get('category') %}active{% endif %}">
                            <a href="{{ url_for('shop') }}">All Products</a>
                        </li>
                        {% for category in categories %}
                        <li class="category-item {% if request.args.get('category') == category %}active{% endif %}">
                            <a href="{{ url_for('shop') }}?category={{ category }}">{{ category }}</a>
                        </li>
                        {% endfor %}
                    </ul>
                </div>
            </div>
            
            <!-- Price Filter -->
            <div class="sidebar mt-4">
                <div class="sidebar-header">
                    <h3>Filter by Price</h3>
                </div>
                <div class="sidebar-body">
                    <form action="{{ url_for('shop') }}" method="GET" class="price-filter-form">
                        <div class="mb-3">
                            <label for="min-price" class="form-label">Min Price:</label>
                            <input type="number" class="form-control" id="min-price" name="min_price" 
                                   value="{{ request.args.get('min_price', '') }}" min="0" step="0.01">
                        </div>
                        <div class="mb-3">
                            <label for="max-price" class="form-label">Max Price:</label>
                            <input type="number" class="form-control" id="max-price" name="max_price" 
                                   value="{{ request.args.get('max_price', '') }}" min="0" step="0.01">
                        </div>
                        <button type="submit" class="btn btn-primary w-100">Apply Filter</button>
                    </form>
                </div>
            </div>
        </div>
        
        <!-- Main Content -->
        <div class="col-lg-9">
            <!-- Page Header -->
            <div class="shop-header mb-4">
                <h2 class="shop-title">
                    {% if request.args.get('category') %}
                        {{ request.args.get('category') }}
                    {% else %}
                        All Products
                    {% endif %}
                </h2>
                <div class="shop-sort">
                    <select class="form-select" id="sort-products">
                        <option value="default">Sort by: Default</option>
                        <option value="price-low">Price: Low to High</option>
                        <option value="price-high">Price: High to Low</option>
                        <option value="name-asc">Name: A to Z</option>
                        <option value="name-desc">Name: Z to A</option>
                    </select>
                </div>
            </div>
            
            <!-- Products Grid -->
            {% if items and items|length > 0 %}
                <!-- Group products by category -->
                {% set categories = {} %}
                {% for item in items %}
                    {% set category = item.get('category', 'Uncategorized') %}
                    {% if category not in categories %}
                        {% set _ = categories.update({category: []}) %}
                    {% endif %}
                    {% set _ = categories[category].append(item) %}
                {% endfor %}
                
                {% for category, category_items in categories.items() %}
                    <div class="category-section mb-5">
                        <h3 class="category-heading">{{ category }}</h3>
                        <div class="row product-grid">
                            {% for item in category_items %}
                            <div class="col-md-4 col-sm-6 mb-4">
                                <div class="product-card">
                                    {% if item.get('featured', false) %}
                                    <div class="product-badge">Featured</div>
                                    {% endif %}
                                    <div class="product-thumb">
                                        <img src="{{ url_for('static', filename='img/' + item.get('image', 'default.jpg')) }}" 
                                             alt="{{ item.get('name', '') }}">
                                    </div>
                                    <div class="product-details">
                                        <span class="product-category">{{ item.get('category', 'Uncategorized') }}</span>
                                        <h4 class="product-title">{{ item.get('name', '') }}</h4>
                                        <div class="product-price">${{ "%.2f"|format(item.get('price', 0)) }}</div>
                                        <div class="product-description">
                                            {{ item.get('description', '')|truncate(60) }}
                                        </div>
                                        {% if item.get('in_stock', false) %}
                                        <form action="{{ url_for('add_to_cart', item_name=item.get('name', '')) }}" method="POST" class="d-flex">
                                            <input type="number" name="quantity" value="1" min="1" max="{{ item.get('quantity', 1) }}" 
                                                   class="form-control form-control-sm me-2" style="width: 70px;">
                                            <button type="submit" class="btn-add-to-cart flex-grow-1">
                                                <i class="fas fa-cart-plus"></i> Add
                                            </button>
                                        </form>
                                        {% else %}
                                        <button class="btn-out-of-stock" disabled>Out of Stock</button>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                {% endfor %}
            {% else %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i> No products found matching your criteria.
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    /* Shop Page Styling */
    .sidebar {
        background-color: #fff;
        border-radius: 8px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.05);
        overflow: hidden;
    }
    
    .sidebar-header {
        padding: 1rem;
        background-color: #f8f9fa;
        border-bottom: 1px solid #e9ecef;
    }
    
    .sidebar-header h3 {
        margin: 0;
        font-size: 1.25rem;
        font-weight: 600;
        color: #333;
    }
    
    .sidebar-body {
        padding: 1rem;
    }
    
    .category-list {
        list-style: none;
        padding: 0;
        margin: 0;
    }
    
    .category-item {
        margin-bottom: 0.5rem;
    }
    
    .category-item a {
        display: block;
        padding: 0.5rem;
        color: #495057;
        text-decoration: none;
        border-radius: 4px;
        transition: all 0.2s ease;
    }
    
    .category-item a:hover {
        background-color: #f8f9fa;
        color: #28a745;
    }
    
    .category-item.active a {
        background-color: #28a745;
        color: white;
    }
    
    /* Shop Header */
    .shop-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-bottom: 1rem;
        border-bottom: 1px solid #e9ecef;
    }
    
    .shop-title {
        margin: 0;
        font-size: 1.5rem;
        font-weight: 600;
        color: #333;
    }
    
    .shop-sort {
        width: 200px;
    }
    
    /* Category Section */
    .category-heading {
        font-size: 1.25rem;
        font-weight: 600;
        color: #333;
        margin-bottom: 1rem;
        padding-bottom: 0.5rem;
        border-bottom: 2px solid #28a745;
        display: inline-block;
    }
    
    /* Product Card Styling */
    .product-card {
        background-color: #fff;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 5px 15px rgba(0,0,0,0.05);
        transition: all 0.3s ease;
        height: 100%;
        position: relative;
    }
    
    .product-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 30px rgba(0,0,0,0.1);
    }
    
    .product-badge {
        position: absolute;
        top: 10px;
        right: 10px;
        background-color: #28a745;
        color: white;
        padding: 5px 10px;
        border-radius: 4px;
        font-size: 0.8rem;
        font-weight: 600;
        z-index: 1;
    }
    
    .product-thumb {
        height: 180px;
        overflow: hidden;
    }
    
    .product-thumb img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.5s ease;
    }
    
    .product-card:hover .product-thumb img {
        transform: scale(1.1);
    }
    
    .product-details {
        padding: 1.25rem;
    }
    
    .product-category {
        display: block;
        font-size: 0.8rem;
        color: #6c757d;
        margin-bottom: 0.5rem;
    }
    
    .product-title {
        font-size: 1.1rem;
        font-weight: 600;
        margin-bottom: 0.5rem;
        color: #333;
    }
    
    .product-price {
        font-size: 1.2rem;
        font-weight: 700;
        color: #28a745;
        margin-bottom: 0.75rem;
    }
    
    .product-description {
        font-size: 0.9rem;
        color: #6c757d;
        margin-bottom: 1rem;
        height: 40px;
        overflow: hidden;
    }
    
    .btn-add-to-cart {
        display: block;
        padding: 0.5rem;
        background-color: #28a745;
        color: white;
        border: none;
        border-radius: 4px;
        font-weight: 600;
        transition: background-color 0.3s ease;
        cursor: pointer;
        text-align: center;
    }
    
    .btn-add-to-cart:hover {
        background-color: #218838;
    }
    
    .btn-out-of-stock {
        display: block;
        width: 100%;
        padding: 0.5rem;
        background-color: #6c757d;
        color: white;
        border: none;
        border-radius: 4px;
        font-weight: 600;
        cursor: not-allowed;
    }
    
    /* Responsive adjustments */
    @media (max-width: 992px) {
        .shop-header {
            flex-direction: column;
            align-items: flex-start;
        }
        
        .shop-sort {
            width: 100%;
            margin-top: 1rem;
        }
    }
    
    @media (max-width: 768px) {
        .product-thumb {
            height: 150px;
        }
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Product sorting functionality
        const sortSelect = document.getElementById('sort-products');
        sortSelect.addEventListener('change', function() {
            const sortValue = this.value;
            const productCards = document.querySelectorAll('.product-card');
            const productGrids = document.querySelectorAll('.product-grid');
            
            productGrids.forEach(grid => {
                const products = Array.from(grid.querySelectorAll('.col-md-4'));
                
                products.sort((a, b) => {
                    const aPrice = parseFloat(a.querySelector('.product-price').textContent.replace('$', ''));
                    const bPrice = parseFloat(b.querySelector('.product-price').textContent.replace('$', ''));
                    const aName = a.querySelector('.product-title').textContent;
                    const bName = b.querySelector('.product-title').textContent;
                    
                    if (sortValue === 'price-low') {
                        return aPrice - bPrice;
                    } else if (sortValue === 'price-high') {
                        return bPrice - aPrice;
                    } else if (sortValue === 'name-asc') {
                        return aName.localeCompare(bName);
                    } else if (sortValue === 'name-desc') {
                        return bName.localeCompare(aName);
                    }
                    
                    return 0;
                });
                
                // Clear the grid and append sorted products
                grid.innerHTML = '';
                products.forEach(product => {
                    grid.appendChild(product);
                });
            });
        });
    });
</script>
{% endblock %}


