{% extends "admin_base.html" %}
{% block title %}Add New Item{% endblock %}
{% block content %}
<div class="container py-4">
    <div class="row">
        <div class="col-md-8 mx-auto">
            <div class="card">
                <div class="card-header">
                    <h3><i class="fas fa-plus-circle"></i> Add New Item</h3>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ url_for('admin_add_item') }}">
                        <div class="mb-3">
                            <label for="name" class="form-label">Item Name</label>
                            <input type="text" class="form-control" id="name" name="name" required>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="price" class="form-label">Price ($)</label>
                                <input type="number" class="form-control" id="price" name="price" 
                                       min="0.01" step="0.01" required>
                            </div>
                            <div class="col-md-6">
                                <label for="quantity" class="form-label">Quantity</label>
                                <input type="number" class="form-control" id="quantity" name="quantity" 
                                       min="0" required>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="category" class="form-label">Category</label>
                            <select class="form-select" id="category" name="category">
                                <option value="Fruits">Fruits</option>
                                <option value="Vegetables">Vegetables</option>
                                <option value="Dairy">Dairy</option>
                                <option value="Bakery">Bakery</option>
                                <option value="Meat">Meat</option>
                                <option value="Beverages">Beverages</option>
                                <option value="Snacks">Snacks</option>
                                <option value="Canned Goods">Canned Goods</option>
                                <option value="Frozen Foods">Frozen Foods</option>
                                <option value="Household">Household</option>
                                <option value="Other">Other</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label for="description" class="form-label">Description</label>
                            <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                        </div>
                        
                        <div class="mb-3">
                            <label for="image" class="form-label">Image Filename</label>
                            <input type="text" class="form-control" id="image" name="image" 
                                   value="default.jpg" placeholder="e.g., apple.jpg">
                            <div class="form-text">Enter the filename of an image in the static/img directory</div>
                        </div>
                        
                        <div class="d-flex justify-content-between">
                            <a href="{{ url_for('admin_inventory') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Back to Inventory
                            </a>
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-plus"></i> Add Item
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}


