{% extends "base.html" %}
{% block title %}Shopping Cart{% endblock %}
{% block content %}
<div class="container py-4">
    <h2 class="mb-4"><i class="fas fa-shopping-cart"></i> Your Shopping Cart</h2>
    
    {% if cart and cart|length > 0 %}
    <div class="card mb-4">
        <div class="card-header">
            <h3>Cart Items</h3>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Item</th>
                            <th>Price</th>
                            <th>Quantity</th>
                            <th>Subtotal</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for item in cart %}
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    <img src="{{ url_for('static', filename='img/' + item.image) }}" 
                                         alt="{{ item.name }}" class="img-thumbnail me-2" style="max-width: 50px;">
                                    {{ item.name }}
                                </div>
                            </td>
                            <td>${{ "%.2f"|format(item.price) }}</td>
                            <td>
                                <form method="POST" action="{{ url_for('update_cart', item_name=item.name) }}" class="d-flex">
                                    <input type="number" name="quantity" value="{{ item.quantity }}" 
                                           class="form-control form-control-sm" style="width: 70px;" min="1">
                                    <button type="submit" class="btn btn-sm btn-primary ms-2">Update</button>
                                </form>
                            </td>
                            <td>${{ "%.2f"|format(item.price * item.quantity) }}</td>
                            <td>
                                <a href="{{ url_for('remove_from_cart', item_name=item.name) }}" 
                                   class="btn btn-sm btn-danger">
                                    <i class="fas fa-trash"></i> Remove
                                </a>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                    <tfoot>
                        <tr>
                            <th colspan="3" class="text-end">Total:</th>
                            <th>${{ "%.2f"|format(total) }}</th>
                            <th></th>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>
        <div class="card-footer d-flex justify-content-between">
            <a href="{{ url_for('shop') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Continue Shopping
            </a>
            <a href="{{ url_for('checkout') }}" class="btn btn-success">
                <i class="fas fa-check-circle"></i> Proceed to Checkout
            </a>
        </div>
    </div>
    {% else %}
    <div class="alert alert-info">
        <p>Your cart is empty. Please add some items to your cart.</p>
        <a href="{{ url_for('shop') }}" class="btn btn-primary">
            <i class="fas fa-shopping-cart"></i> Go Shopping
        </a>
    </div>
    {% endif %}
</div>
{% endblock %}

