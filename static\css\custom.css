/* Custom styles for the grocery store application */
.product-card {
    transition: transform 0.3s;
    height: 100%;
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0,0,0,0.1);
}

.category-badge {
    position: absolute;
    top: 10px;
    right: 10px;
}

.price-tag {
    font-size: 1.2rem;
    font-weight: bold;
    color: #28a745;
}

.stock-info {
    font-size: 0.8rem;
    color: #6c757d;
}

.cart-summary {
    position: sticky;
    top: 20px;
}

.order-status {
    font-size: 0.9rem;
    padding: 5px 10px;
    border-radius: 20px;
}

.status-pending {
    background-color: #ffc107;
    color: #212529;
}

.status-confirmed {
    background-color: #17a2b8;
    color: white;
}

.status-delivered {
    background-color: #28a745;
    color: white;
}

.status-cancelled {
    background-color: #dc3545;
    color: white;
}