{% extends "base.html" %}
{% block title %}Home{% endblock %}
{% block content %}
<div class="container py-4">
    <div class="jumbotron bg-light p-5 rounded mb-5">
        <h1 class="display-4">Welcome to Our Grocery Store!</h1>
        <p class="lead">Find fresh produce, dairy, and pantry essentials at great prices.</p>
        <hr class="my-4">
        <p>Browse our selection and get your groceries delivered to your doorstep.</p>
        <a class="btn btn-primary btn-lg" href="{{ url_for('shop') }}" role="button">
            <i class="fas fa-shopping-cart"></i> Shop Now
        </a>
    </div>

    <!-- Featured Products Section -->
    <section class="featured-section mb-5">
        <div class="container">
            <div class="section-header text-center mb-4">
                <h2 class="section-title">Featured Products</h2>
                <div class="section-divider">
                    <span></span>
                </div>
                <p class="section-subtitle">Handpicked quality items for you</p>
            </div>
            
            <div class="row">
                {% if featured_items and featured_items|length > 0 %}
                    {% for item in featured_items %}
                    <div class="col-md-3 mb-4">
                        <div class="product-card">
                            <div class="product-badge">Featured</div>
                            <div class="product-thumb">
                                <img src="{{ url_for('static', filename='img/' + item.get('image', 'default.jpg')) }}" 
                                     alt="{{ item.get('name', '') }}">
                            </div>
                            <div class="product-details">
                                <span class="product-category">{{ item.get('category', 'Uncategorized') }}</span>
                                <h4 class="product-title">{{ item.get('name', '') }}</h4>
                                <div class="product-price">${{ "%.2f"|format(item.get('price', 0)) }}</div>
                                <div class="product-description">
                                    {{ item.get('description', '')|truncate(60) }}
                                </div>
                                <form action="{{ url_for('add_to_cart', item_name=item.get('name', '')) }}" method="POST">
                                    <input type="hidden" name="quantity" value="1">
                                    <button type="submit" class="btn-add-to-cart">
                                        <i class="fas fa-shopping-cart"></i> Add to Cart
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                    <div class="col-12">
                        <div class="alert alert-info text-center">
                            <i class="fas fa-info-circle"></i> No featured products available at the moment. 
                            <a href="{{ url_for('shop') }}" class="alert-link">Browse our shop</a> to see all products.
                        </div>
                    </div>
                {% endif %}
            </div>
        </div>
    </section>
    
    <!-- Categories Showcase -->
    <section class="categories-section mb-5">
        <div class="container">
            <div class="section-header text-center mb-4">
                <h2 class="section-title">Shop by Category</h2>
                <div class="section-divider">
                    <span></span>
                </div>
                <p class="section-subtitle">Explore our wide range of products</p>
            </div>
            
            <div class="row">
                <div class="col-md-4 mb-4">
                    <div class="category-card" style="background-image: url('{{ url_for('static', filename='img/fruits.jpg') }}')">
                        <div class="category-overlay">
                            <h3 class="category-title">Fruits & Vegetables</h3>
                            <a href="{{ url_for('shop') }}?category=Fruits" class="category-link">Shop Now</a>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 mb-4">
                    <div class="category-card" style="background-image: url('{{ url_for('static', filename='img/dairy.jpg') }}')">
                        <div class="category-overlay">
                            <h3 class="category-title">Dairy & Eggs</h3>
                            <a href="{{ url_for('shop') }}?category=Dairy" class="category-link">Shop Now</a>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 mb-4">
                    <div class="category-card" style="background-image: url('{{ url_for('static', filename='img/bakery.jpg') }}')">
                        <div class="category-overlay">
                            <h3 class="category-title">Bakery</h3>
                            <a href="{{ url_for('shop') }}?category=Bakery" class="category-link">Shop Now</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    
    <!-- Why Choose Us Section -->
    <section class="why-us-section mb-5">
        <div class="container">
            <div class="section-header text-center mb-4">
                <h2 class="section-title">Why Choose Us</h2>
                <div class="section-divider">
                    <span></span>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-4 mb-4">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-truck"></i>
                        </div>
                        <h4 class="feature-title">Free Delivery</h4>
                        <p class="feature-text">Free delivery on orders over $50. Get your groceries delivered to your doorstep.</p>
                    </div>
                </div>
                <div class="col-md-4 mb-4">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-leaf"></i>
                        </div>
                        <h4 class="feature-title">Fresh Products</h4>
                        <p class="feature-text">We ensure all our products are fresh and of the highest quality.</p>
                    </div>
                </div>
                <div class="col-md-4 mb-4">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-headset"></i>
                        </div>
                        <h4 class="feature-title">24/7 Support</h4>
                        <p class="feature-text">Our customer support team is available 24/7 to assist you with any queries.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>
{% endblock %}

{% block extra_css %}
<style>
    /* Featured Products Section */
    .section-header {
        margin-bottom: 2rem;
    }
    
    .section-title {
        font-weight: 700;
        color: #333;
        margin-bottom: 0.5rem;
    }
    
    .section-divider {
        width: 100%;
        text-align: center;
        margin: 0.5rem 0 1rem;
    }
    
    .section-divider span {
        display: inline-block;
        width: 50px;
        height: 3px;
        background-color: #28a745;
    }
    
    .section-subtitle {
        color: #6c757d;
        font-size: 1.1rem;
    }
    
    /* Product Card Styling */
    .product-card {
        background-color: #fff;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 5px 15px rgba(0,0,0,0.05);
        transition: all 0.3s ease;
        height: 100%;
        position: relative;
    }
    
    .product-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 30px rgba(0,0,0,0.1);
    }
    
    .product-badge {
        position: absolute;
        top: 10px;
        right: 10px;
        background-color: #28a745;
        color: white;
        padding: 5px 10px;
        border-radius: 4px;
        font-size: 0.8rem;
        font-weight: 600;
        z-index: 1;
    }
    
    .product-thumb {
        height: 180px;
        overflow: hidden;
    }
    
    .product-thumb img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.5s ease;
    }
    
    .product-card:hover .product-thumb img {
        transform: scale(1.1);
    }
    
    .product-details {
        padding: 1.25rem;
    }
    
    .product-category {
        display: block;
        font-size: 0.8rem;
        color: #6c757d;
        margin-bottom: 0.5rem;
    }
    
    .product-title {
        font-size: 1.1rem;
        font-weight: 600;
        margin-bottom: 0.5rem;
        color: #333;
    }
    
    .product-price {
        font-size: 1.2rem;
        font-weight: 700;
        color: #28a745;
        margin-bottom: 0.75rem;
    }
    
    .product-description {
        font-size: 0.9rem;
        color: #6c757d;
        margin-bottom: 1rem;
        height: 40px;
        overflow: hidden;
    }
    
    .btn-add-to-cart {
        display: block;
        width: 100%;
        padding: 0.5rem;
        background-color: #28a745;
        color: white;
        border: none;
        border-radius: 4px;
        font-weight: 600;
        transition: background-color 0.3s ease;
        cursor: pointer;
    }
    
    .btn-add-to-cart:hover {
        background-color: #218838;
    }
    
    /* Category Cards */
    .category-card {
        height: 200px;
        border-radius: 8px;
        overflow: hidden;
        position: relative;
        background-size: cover;
        background-position: center;
    }
    
    .category-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.4);
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        transition: background 0.3s ease;
    }
    
    .category-card:hover .category-overlay {
        background: rgba(0,0,0,0.6);
    }
    
    .category-title {
        color: white;
        font-size: 1.5rem;
        font-weight: 700;
        margin-bottom: 1rem;
        text-align: center;
    }
    
    .category-link {
        display: inline-block;
        padding: 0.5rem 1.5rem;
        background-color: #28a745;
        color: white;
        border-radius: 4px;
        font-weight: 600;
        transition: all 0.3s ease;
        text-decoration: none;
    }
    
    .category-link:hover {
        background-color: #218838;
        transform: translateY(-2px);
        color: white;
    }
    
    /* Feature Cards */
    .feature-card {
        background-color: #fff;
        border-radius: 8px;
        padding: 2rem;
        text-align: center;
        box-shadow: 0 5px 15px rgba(0,0,0,0.05);
        transition: transform 0.3s ease;
        height: 100%;
    }
    
    .feature-card:hover {
        transform: translateY(-5px);
    }
    
    .feature-icon {
        font-size: 2.5rem;
        color: #28a745;
        margin-bottom: 1rem;
    }
    
    .feature-title {
        font-size: 1.2rem;
        font-weight: 600;
        margin-bottom: 1rem;
        color: #333;
    }
    
    .feature-text {
        color: #6c757d;
        font-size: 0.9rem;
    }
    
    /* Responsive adjustments */
    @media (max-width: 768px) {
        .product-thumb {
            height: 150px;
        }
        
        .category-card {
            height: 150px;
            margin-bottom: 1rem;
        }
        
        .feature-card {
            padding: 1.5rem;
            margin-bottom: 1rem;
        }
    }
</style>
{% endblock %}



