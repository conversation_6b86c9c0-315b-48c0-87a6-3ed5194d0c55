{% extends "base.html" %}
{% block title %}Debug Orders{% endblock %}
{% block content %}
<div class="container py-4">
    <h1>Debug Orders</h1>
    <div class="alert alert-warning">
        <strong>Warning:</strong> This is a debug page and should not be accessible in production.
    </div>
    
    <div class="mb-3">
        <a href="{{ url_for('debug_clear_orders') }}" class="btn btn-danger">Clear All Orders</a>
        <a href="{{ url_for('debug_fix_orders') }}" class="btn btn-warning">Fix Orders File</a>
        <a href="{{ url_for('admin_dashboard') }}" class="btn btn-primary">Back to Dashboard</a>
    </div>
    
    <div class="card">
        <div class="card-header">
            <h3>Raw Orders Data</h3>
        </div>
        <div class="card-body">
            <pre class="bg-light p-3">{{ orders|tojson(indent=2) }}</pre>
        </div>
    </div>
</div>
{% endblock %}
