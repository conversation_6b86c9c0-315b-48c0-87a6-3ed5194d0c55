{% extends "base.html" %}
{% block title %}Order Details{% endblock %}
{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>Order Details</h1>
    <a href="{{ url_for('admin_dashboard') }}" class="btn btn-secondary">Back to Dashboard</a>
</div>

<div class="row">
    <div class="col-md-6">
        <div class="card mb-4">
            <div class="card-header">
                <h5>Customer Information</h5>
            </div>
            <div class="card-body">
                <p><strong>Name:</strong> {{ order.customer_name }}</p>
                <p><strong>Phone:</strong> {{ order.phone }}</p>
                <p><strong>Address:</strong> {{ order.address }}</p>
                <p><strong>Order Date:</strong> {{ order.date }}</p>
                <p><strong>Order ID:</strong> {{ order.order_id }}</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card mb-4">
            <div class="card-header">
                <h5>Order Status</h5>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('admin_update_order', order_id=order.order_id) }}">
                    <div class="mb-3">
                        <label for="status" class="form-label">Current Status:</label>
                        <select class="form-select" id="status" name="status">
                            <option value="pending" {% if order.status == 'pending' %}selected{% endif %}>Pending</option>
                            <option value="confirmed" {% if order.status == 'confirmed' %}selected{% endif %}>Confirmed</option>
                            <option value="delivered" {% if order.status == 'delivered' %}selected{% endif %}>Delivered</option>
                            <option value="cancelled" {% if order.status == 'cancelled' %}selected{% endif %}>Cancelled</option>
                        </select>
                    </div>
                    <button type="submit" class="btn btn-primary">Update Status</button>
                </form>
            </div>
        </div>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <h5>Order Items</h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>Item</th>
                        <th>Category</th>
                        <th>Price</th>
                        <th>Quantity</th>
                        <th>Subtotal</th>
                    </tr>
                </thead>
                <tbody>
                    {% for item in order.items %}
                    <tr>
                        <td>{{ item.name }}</td>
                        <td>{{ item.category }}</td>
                        <td>${{ item.price }}</td>
                        <td>{{ item.quantity }}</td>
                        <td>${{ item.price * item.quantity }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
                <tfoot>
                    <tr>
                        <td colspan="4" class="text-end"><strong>Total:</strong></td>
                        <td><strong>${{ order.total }}</strong></td>
                    </tr>
                </tfoot>
            </table>
        </div>
    </div>
</div>
{% endblock %}