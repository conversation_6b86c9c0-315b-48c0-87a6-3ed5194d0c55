/* Custom styles for the grocery store application */

/* General styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
}

/* Navbar customization */
.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
}

/* Card hover effects */
.card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

/* Product image styling */
.card-img-top {
    transition: transform 0.3s ease;
}

.card:hover .card-img-top {
    transform: scale(1.05);
}

/* Footer styling */
footer {
    margin-top: 2rem;
}

footer a {
    text-decoration: none;
    transition: color 0.3s ease;
}

footer a:hover {
    color: #adb5bd !important;
    text-decoration: underline;
}

/* Admin dashboard cards */
.admin-card {
    border-radius: 10px;
    overflow: hidden;
}

/* Custom button styles */
.btn-primary {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

.btn-primary:hover {
    background-color: #0b5ed7;
    border-color: #0a58ca;
}

/* Form styling */
.form-control:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* Table styling */
.table-responsive {
    overflow-x: auto;
}

/* Jumbotron styling */
.jumbotron {
    background-color: #e9ecef;
    border-radius: 0.3rem;
    padding: 2rem 1rem;
}

/* Badge styling */
.badge {
    font-weight: 500;
    padding: 0.35em 0.65em;
}

/* Alert styling */
.alert {
    border-radius: 0.25rem;
    padding: 1rem;
}

/* Product Card Styles */
.product-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border: 1px solid rgba(0,0,0,0.125);
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.price-tag {
    font-weight: bold;
    color: #28a745;
    font-size: 1.2rem;
}

/* Featured Products Section */
.featured-product-section {
    background-color: #f8f9fa;
    padding: 3rem 0;
}

/* Media queries for responsive design */
@media (max-width: 768px) {
    .jumbotron {
        padding: 1.5rem;
    }
    
    .display-4 {
        font-size: 2.5rem;
    }
}
