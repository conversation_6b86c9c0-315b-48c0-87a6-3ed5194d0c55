document.addEventListener('DOMContentLoaded', function() {
    const checkoutForm = document.getElementById('checkout-form');
    
    if (checkoutForm) {
        checkoutForm.addEventListener('submit', function(event) {
            const name = document.getElementById('name').value.trim();
            const email = document.getElementById('email').value.trim();
            const phone = document.getElementById('phone').value.trim();
            const address = document.getElementById('address').value.trim();
            
            let isValid = true;
            let errorMessage = '';
            
            if (!name) {
                isValid = false;
                errorMessage += 'Please enter your full name.\n';
            }
            
            if (!email || !email.includes('@')) {
                isValid = false;
                errorMessage += 'Please enter a valid email address.\n';
            }
            
            if (!phone) {
                isValid = false;
                errorMessage += 'Please enter your phone number.\n';
            }
            
            if (!address) {
                isValid = false;
                errorMessage += 'Please enter your delivery address.\n';
            }
            
            if (!isValid) {
                event.preventDefault();
                alert('Please fix the following errors:\n' + errorMessage);
            }
        });
    }
});