{% extends "base.html" %}
{% block title %}Admin Dashboard{% endblock %}
{% block content %}
<div class="container py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2><i class="fas fa-tachometer-alt"></i> Admin Dashboard</h2>
        <a href="{{ url_for('admin_logout') }}" class="btn btn-danger">
            <i class="fas fa-sign-out-alt"></i> Logout
        </a>
    </div>

    <div class="row mb-4">
        <div class="col-md-4">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <h5 class="card-title">Pending Orders</h5>
                    <p class="card-text display-4">{{ pending_count|default(0) }}</p>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <h5 class="card-title">Confirmed Orders</h5>
                    <p class="card-text display-4">{{ confirmed_count|default(0) }}</p>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <h5 class="card-title">Delivered Orders</h5>
                    <p class="card-text display-4">{{ delivered_count|default(0) }}</p>
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3>Recent Orders</h3>
                    <div>
                        <a href="{{ url_for('admin_inventory') }}" class="btn btn-primary">
                            <i class="fas fa-boxes"></i> Manage Inventory
                        </a>
                        {% if config.DEBUG %}
                        <a href="{{ url_for('debug_fix_orders') }}" class="btn btn-warning ms-2">
                            <i class="fas fa-wrench"></i> Fix Orders
                        </a>
                        {% endif %}
                    </div>
                </div>
                <div class="card-body">
                    {% if orders and orders|length > 0 %}
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Order ID</th>
                                    <th>Date</th>
                                    <th>Customer</th>
                                    <th>Items</th>
                                    <th>Total</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for order in orders %}
                                <tr>
                                    <td>{{ order.order_id[:8] if order.order_id else 'N/A' }}...</td>
                                    <td>{{ order.date|default('N/A') }}</td>
                                    <td>{{ order.name|default('Not provided') }}</td>
                                    <td>
                                        {% if order.items and order.items is iterable %}
                                            {{ order.items|length }} items
                                        {% else %}
                                            0 items
                                        {% endif %}
                                    </td>
                                    <td>${{ "%.2f"|format(order.total) if order.total else '0.00' }}</td>
                                    <td>
                                        {% if order.status == 'pending' %}
                                        <span class="badge bg-warning">Pending</span>
                                        {% elif order.status == 'confirmed' %}
                                        <span class="badge bg-info">Confirmed</span>
                                        {% elif order.status == 'delivered' %}
                                        <span class="badge bg-success">Delivered</span>
                                        {% elif order.status == 'cancelled' %}
                                        <span class="badge bg-danger">Cancelled</span>
                                        {% else %}
                                        <span class="badge bg-secondary">{{ order.status|default('Unknown') }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <form method="POST" action="{{ url_for('update_order_status', order_id=order.order_id) }}" class="d-inline">
                                            <select name="status" class="form-select form-select-sm d-inline-block" style="width: auto;">
                                                <option value="pending" {% if order.status == 'pending' %}selected{% endif %}>Pending</option>
                                                <option value="confirmed" {% if order.status == 'confirmed' %}selected{% endif %}>Confirmed</option>
                                                <option value="delivered" {% if order.status == 'delivered' %}selected{% endif %}>Delivered</option>
                                                <option value="cancelled" {% if order.status == 'cancelled' %}selected{% endif %}>Cancelled</option>
                                            </select>
                                            <button type="submit" class="btn btn-sm btn-primary">Update</button>
                                        </form>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="alert alert-info">No orders found.</div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
{% endblock %}

