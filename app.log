2025-06-24 20:42:17,896 - werkzeug - WARNING -  * Debu<PERSON> is active!
2025-06-24 20:42:17,905 - werkzeug - INFO -  * Debugger PIN: 272-114-452
2025-06-24 20:44:40,027 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:44:40] "GET /order_confirmation/94eeb0b4-b216-4b39-8b04-4eb704c6bcc0 HTTP/1.1" 200 -
2025-06-24 20:44:40,230 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:44:40] "[36mGET /static/js/checkout.js HTTP/1.1[0m" 304 -
2025-06-24 20:44:40,233 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:44:40] "[36mGET /static/css/custom.css HTTP/1.1[0m" 304 -
2025-06-24 20:44:40,639 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:44:40] "GET /order_confirmation/94eeb0b4-b216-4b39-8b04-4eb704c6bcc0 HTTP/1.1" 200 -
2025-06-24 20:44:41,343 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:44:41] "GET /checkout HTTP/1.1" 200 -
2025-06-24 20:44:41,458 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:44:41] "GET /checkout HTTP/1.1" 200 -
2025-06-24 20:44:43,274 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:44:43] "GET / HTTP/1.1" 200 -
2025-06-24 20:44:43,344 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:44:43] "[36mGET /static/css/custom.css HTTP/1.1[0m" 304 -
2025-06-24 20:44:43,346 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:44:43] "[36mGET /static/js/checkout.js HTTP/1.1[0m" 304 -
2025-06-24 20:44:43,406 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:44:43] "GET / HTTP/1.1" 200 -
2025-06-24 20:46:42,105 - app - ERROR - Unhandled exception: Unexpected end of template. Jinja was looking for the following tags: 'endblock'. The innermost block that needs to be closed is 'block'.
2025-06-24 20:46:42,125 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:46:42] "[35m[1mGET / HTTP/1.1[0m" 500 -
2025-06-24 20:46:42,356 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:46:42] "[36mGET /static/css/custom.css HTTP/1.1[0m" 304 -
2025-06-24 20:46:42,365 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:46:42] "[36mGET /static/js/checkout.js HTTP/1.1[0m" 304 -
2025-06-24 20:46:42,602 - app - ERROR - Unhandled exception: Unexpected end of template. Jinja was looking for the following tags: 'endblock'. The innermost block that needs to be closed is 'block'.
2025-06-24 20:46:42,604 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:46:42] "[35m[1mGET / HTTP/1.1[0m" 500 -
2025-06-24 20:46:44,436 - app - ERROR - Unhandled exception: Unexpected end of template. Jinja was looking for the following tags: 'endblock'. The innermost block that needs to be closed is 'block'.
2025-06-24 20:46:44,438 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:46:44] "[35m[1mGET / HTTP/1.1[0m" 500 -
2025-06-24 20:46:44,488 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:46:44] "[36mGET /static/css/custom.css HTTP/1.1[0m" 304 -
2025-06-24 20:46:44,489 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:46:44] "[36mGET /static/js/checkout.js HTTP/1.1[0m" 304 -
2025-06-24 20:46:44,549 - app - ERROR - Unhandled exception: Unexpected end of template. Jinja was looking for the following tags: 'endblock'. The innermost block that needs to be closed is 'block'.
2025-06-24 20:46:44,552 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:46:44] "[35m[1mGET / HTTP/1.1[0m" 500 -
2025-06-24 20:46:45,067 - app - ERROR - Unhandled exception: Unexpected end of template. Jinja was looking for the following tags: 'endblock'. The innermost block that needs to be closed is 'block'.
2025-06-24 20:46:45,069 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:46:45] "[35m[1mGET / HTTP/1.1[0m" 500 -
2025-06-24 20:46:45,114 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:46:45] "[36mGET /static/js/checkout.js HTTP/1.1[0m" 304 -
2025-06-24 20:46:45,118 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:46:45] "[36mGET /static/css/custom.css HTTP/1.1[0m" 304 -
2025-06-24 20:46:45,200 - app - ERROR - Unhandled exception: Unexpected end of template. Jinja was looking for the following tags: 'endblock'. The innermost block that needs to be closed is 'block'.
2025-06-24 20:46:45,203 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:46:45] "[35m[1mGET / HTTP/1.1[0m" 500 -
2025-06-24 20:46:45,780 - app - ERROR - Unhandled exception: Unexpected end of template. Jinja was looking for the following tags: 'endblock'. The innermost block that needs to be closed is 'block'.
2025-06-24 20:46:45,785 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:46:45] "[35m[1mGET / HTTP/1.1[0m" 500 -
2025-06-24 20:46:45,837 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:46:45] "[36mGET /static/js/checkout.js HTTP/1.1[0m" 304 -
2025-06-24 20:46:45,839 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:46:45] "[36mGET /static/css/custom.css HTTP/1.1[0m" 304 -
2025-06-24 20:46:45,881 - app - ERROR - Unhandled exception: Unexpected end of template. Jinja was looking for the following tags: 'endblock'. The innermost block that needs to be closed is 'block'.
2025-06-24 20:46:45,883 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:46:45] "[35m[1mGET / HTTP/1.1[0m" 500 -
2025-06-24 20:46:46,690 - app - ERROR - Error in shop route: 'dict object' has no attribute 'image'
2025-06-24 20:46:46,699 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:46:46] "[32mGET /shop HTTP/1.1[0m" 302 -
2025-06-24 20:46:46,762 - app - ERROR - Unhandled exception: Unexpected end of template. Jinja was looking for the following tags: 'endblock'. The innermost block that needs to be closed is 'block'.
2025-06-24 20:46:46,771 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:46:46] "[35m[1mGET / HTTP/1.1[0m" 500 -
2025-06-24 20:46:47,046 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:46:47] "[36mGET /static/js/checkout.js HTTP/1.1[0m" 304 -
2025-06-24 20:46:47,199 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:46:47] "[36mGET /static/css/custom.css HTTP/1.1[0m" 304 -
2025-06-24 20:46:47,366 - app - ERROR - Unhandled exception: Unexpected end of template. Jinja was looking for the following tags: 'endblock'. The innermost block that needs to be closed is 'block'.
2025-06-24 20:46:47,368 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:46:47] "[35m[1mGET / HTTP/1.1[0m" 500 -
2025-06-24 20:46:48,337 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:46:48] "GET /cart HTTP/1.1" 200 -
2025-06-24 20:46:48,388 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:46:48] "[36mGET /static/js/checkout.js HTTP/1.1[0m" 304 -
2025-06-24 20:46:48,394 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:46:48] "[36mGET /static/css/custom.css HTTP/1.1[0m" 304 -
2025-06-24 20:46:48,454 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:46:48] "GET /cart HTTP/1.1" 200 -
2025-06-24 20:46:50,164 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:46:50] "GET /admin/login HTTP/1.1" 200 -
2025-06-24 20:46:50,251 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:46:50] "[36mGET /static/js/checkout.js HTTP/1.1[0m" 304 -
2025-06-24 20:46:50,253 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:46:50] "[36mGET /static/css/custom.css HTTP/1.1[0m" 304 -
2025-06-24 20:46:50,897 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:46:50] "GET /admin/login HTTP/1.1" 200 -
2025-06-24 20:46:51,361 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:46:51] "[32mPOST /admin/login HTTP/1.1[0m" 302 -
2025-06-24 20:46:51,455 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:46:51] "GET /admin/dashboard HTTP/1.1" 200 -
2025-06-24 20:46:51,522 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:46:51] "[36mGET /static/css/custom.css HTTP/1.1[0m" 304 -
2025-06-24 20:46:51,528 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:46:51] "[36mGET /static/js/checkout.js HTTP/1.1[0m" 304 -
2025-06-24 20:46:51,698 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:46:51] "GET /admin/dashboard HTTP/1.1" 200 -
2025-06-24 20:46:59,887 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:46:59] "[32mPOST /admin/update_order/94eeb0b4-b216-4b39-8b04-4eb704c6bcc0 HTTP/1.1[0m" 302 -
2025-06-24 20:46:59,953 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:46:59] "GET /admin/dashboard HTTP/1.1" 200 -
2025-06-24 20:47:00,084 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:47:00] "[36mGET /static/js/checkout.js HTTP/1.1[0m" 304 -
2025-06-24 20:47:00,130 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:47:00] "[36mGET /static/css/custom.css HTTP/1.1[0m" 304 -
2025-06-24 20:47:00,232 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:47:00] "GET /admin/dashboard HTTP/1.1" 200 -
2025-06-24 20:47:17,151 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:47:17] "GET /cart HTTP/1.1" 200 -
2025-06-24 20:47:17,238 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:47:17] "[36mGET /static/js/checkout.js HTTP/1.1[0m" 304 -
2025-06-24 20:47:17,239 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:47:17] "[36mGET /static/css/custom.css HTTP/1.1[0m" 304 -
2025-06-24 20:47:17,285 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:47:17] "GET /cart HTTP/1.1" 200 -
2025-06-24 20:47:18,747 - app - ERROR - Unhandled exception: Unexpected end of template. Jinja was looking for the following tags: 'endblock'. The innermost block that needs to be closed is 'block'.
2025-06-24 20:47:18,750 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:47:18] "[35m[1mGET / HTTP/1.1[0m" 500 -
2025-06-24 20:47:18,818 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:47:18] "[36mGET /static/css/custom.css HTTP/1.1[0m" 304 -
2025-06-24 20:47:18,821 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:47:18] "[36mGET /static/js/checkout.js HTTP/1.1[0m" 304 -
2025-06-24 20:47:18,872 - app - ERROR - Unhandled exception: Unexpected end of template. Jinja was looking for the following tags: 'endblock'. The innermost block that needs to be closed is 'block'.
2025-06-24 20:47:18,897 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:47:18] "[35m[1mGET / HTTP/1.1[0m" 500 -
2025-06-24 20:47:19,421 - app - ERROR - Unhandled exception: Unexpected end of template. Jinja was looking for the following tags: 'endblock'. The innermost block that needs to be closed is 'block'.
2025-06-24 20:47:19,422 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:47:19] "[35m[1mGET / HTTP/1.1[0m" 500 -
2025-06-24 20:47:19,486 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:47:19] "[36mGET /static/css/custom.css HTTP/1.1[0m" 304 -
2025-06-24 20:47:19,494 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:47:19] "[36mGET /static/js/checkout.js HTTP/1.1[0m" 304 -
2025-06-24 20:47:19,522 - app - ERROR - Unhandled exception: Unexpected end of template. Jinja was looking for the following tags: 'endblock'. The innermost block that needs to be closed is 'block'.
2025-06-24 20:47:19,535 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:47:19] "[35m[1mGET / HTTP/1.1[0m" 500 -
2025-06-24 20:47:22,787 - app - ERROR - Unhandled exception: Unexpected end of template. Jinja was looking for the following tags: 'endblock'. The innermost block that needs to be closed is 'block'.
2025-06-24 20:47:22,793 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:47:22] "[35m[1mGET / HTTP/1.1[0m" 500 -
2025-06-24 20:47:22,863 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:47:22] "[36mGET /static/css/custom.css HTTP/1.1[0m" 304 -
2025-06-24 20:47:22,865 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:47:22] "[36mGET /static/js/checkout.js HTTP/1.1[0m" 304 -
2025-06-24 20:47:22,954 - app - ERROR - Unhandled exception: Unexpected end of template. Jinja was looking for the following tags: 'endblock'. The innermost block that needs to be closed is 'block'.
2025-06-24 20:47:22,956 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:47:22] "[35m[1mGET / HTTP/1.1[0m" 500 -
2025-06-24 20:47:24,405 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:47:24] "GET /admin/login HTTP/1.1" 200 -
2025-06-24 20:47:24,465 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:47:24] "[36mGET /static/css/custom.css HTTP/1.1[0m" 304 -
2025-06-24 20:47:24,483 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:47:24] "[36mGET /static/js/checkout.js HTTP/1.1[0m" 304 -
2025-06-24 20:47:24,555 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:47:24] "GET /admin/login HTTP/1.1" 200 -
2025-06-24 20:47:25,997 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:47:25] "[32mPOST /admin/login HTTP/1.1[0m" 302 -
2025-06-24 20:47:26,112 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:47:26] "GET /admin/dashboard HTTP/1.1" 200 -
2025-06-24 20:47:26,220 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:47:26] "[36mGET /static/css/custom.css HTTP/1.1[0m" 304 -
2025-06-24 20:47:26,221 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:47:26] "[36mGET /static/js/checkout.js HTTP/1.1[0m" 304 -
2025-06-24 20:47:26,281 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:47:26] "GET /admin/dashboard HTTP/1.1" 200 -
2025-06-24 20:47:31,765 - app - ERROR - Error in admin_inventory: 'dict object' has no attribute 'image'
2025-06-24 20:47:31,766 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:47:31] "[32mGET /admin/inventory HTTP/1.1[0m" 302 -
2025-06-24 20:47:31,780 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:47:31] "GET /admin/dashboard HTTP/1.1" 200 -
2025-06-24 20:47:31,833 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:47:31] "[36mGET /static/js/checkout.js HTTP/1.1[0m" 304 -
2025-06-24 20:47:31,833 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:47:31] "[36mGET /static/css/custom.css HTTP/1.1[0m" 304 -
2025-06-24 20:47:32,149 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:47:32] "GET /admin/dashboard HTTP/1.1" 200 -
2025-06-24 20:47:34,769 - app - ERROR - Error in admin_inventory: 'dict object' has no attribute 'image'
2025-06-24 20:47:34,770 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:47:34] "[32mGET /admin/inventory HTTP/1.1[0m" 302 -
2025-06-24 20:47:34,785 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:47:34] "GET /admin/dashboard HTTP/1.1" 200 -
2025-06-24 20:47:34,835 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:47:34] "[36mGET /static/js/checkout.js HTTP/1.1[0m" 304 -
2025-06-24 20:47:34,836 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:47:34] "[36mGET /static/css/custom.css HTTP/1.1[0m" 304 -
2025-06-24 20:47:34,872 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:47:34] "GET /admin/dashboard HTTP/1.1" 200 -
2025-06-24 20:47:37,551 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:47:37] "[32mGET /debug/fix_orders HTTP/1.1[0m" 302 -
2025-06-24 20:47:37,566 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:47:37] "GET /admin/dashboard HTTP/1.1" 200 -
2025-06-24 20:47:37,635 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:47:37] "[36mGET /static/js/checkout.js HTTP/1.1[0m" 304 -
2025-06-24 20:47:37,647 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:47:37] "[36mGET /static/css/custom.css HTTP/1.1[0m" 304 -
2025-06-24 20:47:37,705 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:47:37] "GET /admin/dashboard HTTP/1.1" 200 -
2025-06-24 20:47:40,660 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:47:40] "[32mGET /debug/fix_orders HTTP/1.1[0m" 302 -
2025-06-24 20:47:40,669 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:47:40] "GET /admin/dashboard HTTP/1.1" 200 -
2025-06-24 20:47:40,719 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:47:40] "[36mGET /static/css/custom.css HTTP/1.1[0m" 304 -
2025-06-24 20:47:40,727 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:47:40] "[36mGET /static/js/checkout.js HTTP/1.1[0m" 304 -
2025-06-24 20:47:40,758 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:47:40] "GET /admin/dashboard HTTP/1.1" 200 -
2025-06-24 20:47:42,077 - app - ERROR - Error in admin_inventory: 'dict object' has no attribute 'image'
2025-06-24 20:47:42,079 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:47:42] "[32mGET /admin/inventory HTTP/1.1[0m" 302 -
2025-06-24 20:47:42,089 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:47:42] "GET /admin/dashboard HTTP/1.1" 200 -
2025-06-24 20:47:42,151 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:47:42] "[36mGET /static/css/custom.css HTTP/1.1[0m" 304 -
2025-06-24 20:47:42,153 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:47:42] "[36mGET /static/js/checkout.js HTTP/1.1[0m" 304 -
2025-06-24 20:47:42,184 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:47:42] "GET /admin/dashboard HTTP/1.1" 200 -
2025-06-24 20:47:44,799 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:47:44] "GET /admin/dashboard HTTP/1.1" 200 -
2025-06-24 20:47:44,912 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:47:44] "GET /admin/dashboard HTTP/1.1" 200 -
2025-06-24 20:47:45,526 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:47:45] "GET /admin/dashboard HTTP/1.1" 200 -
2025-06-24 20:47:46,714 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:47:46] "GET /admin/dashboard HTTP/1.1" 200 -
2025-06-24 20:47:47,432 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:47:47] "GET /admin/dashboard HTTP/1.1" 200 -
2025-06-24 20:47:48,040 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:47:48] "GET /admin/dashboard HTTP/1.1" 200 -
2025-06-24 20:47:50,782 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:47:50] "[32mPOST /admin/login HTTP/1.1[0m" 302 -
2025-06-24 20:47:50,801 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:47:50] "GET /admin/dashboard HTTP/1.1" 200 -
2025-06-24 20:47:50,951 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:47:50] "[36mGET /static/css/custom.css HTTP/1.1[0m" 304 -
2025-06-24 20:47:50,951 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:47:50] "[36mGET /static/js/checkout.js HTTP/1.1[0m" 304 -
2025-06-24 20:47:50,997 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:47:50] "GET /admin/dashboard HTTP/1.1" 200 -
2025-06-24 20:47:59,715 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:47:59] "GET / HTTP/1.1" 200 -
2025-06-24 20:47:59,837 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:47:59] "[36mGET /static/css/custom.css HTTP/1.1[0m" 304 -
2025-06-24 20:47:59,845 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:47:59] "[36mGET /static/js/checkout.js HTTP/1.1[0m" 304 -
2025-06-24 20:47:59,952 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:47:59] "GET / HTTP/1.1" 200 -
2025-06-24 20:48:03,393 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:48:03] "GET / HTTP/1.1" 200 -
2025-06-24 20:48:03,503 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:48:03] "[36mGET /static/css/custom.css HTTP/1.1[0m" 304 -
2025-06-24 20:48:03,509 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:48:03] "[36mGET /static/js/checkout.js HTTP/1.1[0m" 304 -
2025-06-24 20:48:03,563 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:48:03] "GET / HTTP/1.1" 200 -
2025-06-24 20:48:49,569 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:48:49] "GET / HTTP/1.1" 200 -
2025-06-24 20:48:49,635 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:48:49] "GET /static/css/style.css HTTP/1.1" 200 -
2025-06-24 20:48:49,637 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:48:49] "GET /static/js/script.js HTTP/1.1" 200 -
2025-06-24 20:48:50,569 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:48:50] "GET / HTTP/1.1" 200 -
2025-06-24 20:48:57,525 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:48:57] "GET / HTTP/1.1" 200 -
2025-06-24 20:48:57,576 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:48:57] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-24 20:48:57,579 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:48:57] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-24 20:48:57,628 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:48:57] "GET / HTTP/1.1" 200 -
2025-06-24 20:49:00,052 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:49:00] "GET /admin/login HTTP/1.1" 200 -
2025-06-24 20:49:00,108 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:49:00] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-24 20:49:00,121 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:49:00] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-24 20:49:00,157 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:49:00] "GET /admin/login HTTP/1.1" 200 -
2025-06-24 20:49:01,417 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:49:01] "[32mPOST /admin/login HTTP/1.1[0m" 302 -
2025-06-24 20:49:01,432 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:49:01] "GET /admin/dashboard HTTP/1.1" 200 -
2025-06-24 20:49:01,528 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:49:01] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-24 20:49:01,531 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:49:01] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-24 20:49:01,561 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:49:01] "GET /admin/dashboard HTTP/1.1" 200 -
2025-06-24 20:49:04,534 - app - ERROR - Error in admin_inventory: 'dict object' has no attribute 'image'
2025-06-24 20:49:04,537 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:49:04] "[32mGET /admin/inventory HTTP/1.1[0m" 302 -
2025-06-24 20:49:04,550 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:49:04] "GET /admin/dashboard HTTP/1.1" 200 -
2025-06-24 20:49:04,615 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:49:04] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-24 20:49:04,617 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:49:04] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-24 20:49:04,666 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:49:04] "GET /admin/dashboard HTTP/1.1" 200 -
2025-06-24 20:49:06,645 - app - ERROR - Error in admin_inventory: 'dict object' has no attribute 'image'
2025-06-24 20:49:06,647 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:49:06] "[32mGET /admin/inventory HTTP/1.1[0m" 302 -
2025-06-24 20:49:06,663 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:49:06] "GET /admin/dashboard HTTP/1.1" 200 -
2025-06-24 20:49:06,748 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:49:06] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-24 20:49:06,749 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:49:06] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-24 20:49:06,783 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:49:06] "GET /admin/dashboard HTTP/1.1" 200 -
2025-06-24 20:49:07,883 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:49:07] "[32mGET /debug/fix_orders HTTP/1.1[0m" 302 -
2025-06-24 20:49:07,898 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:49:07] "GET /admin/dashboard HTTP/1.1" 200 -
2025-06-24 20:49:07,946 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:49:07] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-24 20:49:07,953 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:49:07] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-24 20:49:08,015 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:49:08] "GET /admin/dashboard HTTP/1.1" 200 -
2025-06-24 20:49:15,391 - app - ERROR - Error in admin_inventory: 'dict object' has no attribute 'image'
2025-06-24 20:49:15,394 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:49:15] "[32mGET /admin/inventory HTTP/1.1[0m" 302 -
2025-06-24 20:49:15,407 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:49:15] "GET /admin/dashboard HTTP/1.1" 200 -
2025-06-24 20:49:15,508 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:49:15] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-24 20:49:15,508 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:49:15] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-24 20:49:15,543 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:49:15] "GET /admin/dashboard HTTP/1.1" 200 -
2025-06-24 20:52:12,934 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:52:12] "GET /admin/dashboard HTTP/1.1" 200 -
2025-06-24 20:52:12,981 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:52:12] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-24 20:52:12,986 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:52:12] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-24 20:52:13,055 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:52:13] "GET /admin/dashboard HTTP/1.1" 200 -
2025-06-24 20:52:13,968 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:52:13] "GET / HTTP/1.1" 200 -
2025-06-24 20:52:14,008 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:52:14] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-24 20:52:14,012 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:52:14] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-24 20:52:14,065 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:52:14] "GET / HTTP/1.1" 200 -
2025-06-24 20:52:16,761 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:52:16] "GET /admin/login HTTP/1.1" 200 -
2025-06-24 20:52:16,847 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:52:16] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-24 20:52:16,854 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:52:16] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-24 20:52:16,936 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:52:16] "GET /admin/login HTTP/1.1" 200 -
2025-06-24 20:52:18,721 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:52:18] "[32mPOST /admin/login HTTP/1.1[0m" 302 -
2025-06-24 20:52:18,738 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:52:18] "GET /admin/dashboard HTTP/1.1" 200 -
2025-06-24 20:52:18,840 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:52:18] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-24 20:52:18,848 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:52:18] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-24 20:52:18,881 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:52:18] "GET /admin/dashboard HTTP/1.1" 200 -
2025-06-24 20:52:22,217 - app - ERROR - Error in admin_inventory: Could not build url for endpoint 'admin_orders'. Did you mean 'admin_redirect' instead?
2025-06-24 20:52:22,220 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:52:22] "[32mGET /admin/inventory HTTP/1.1[0m" 302 -
2025-06-24 20:52:22,239 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:52:22] "GET /admin/dashboard HTTP/1.1" 200 -
2025-06-24 20:52:22,325 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:52:22] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-24 20:52:22,326 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:52:22] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-24 20:52:22,351 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:52:22] "GET /admin/dashboard HTTP/1.1" 200 -
2025-06-24 20:52:26,900 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:52:26] "[32mGET /debug/fix_orders HTTP/1.1[0m" 302 -
2025-06-24 20:52:26,912 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:52:26] "GET /admin/dashboard HTTP/1.1" 200 -
2025-06-24 20:52:26,977 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:52:26] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-24 20:52:26,989 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:52:26] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-24 20:52:27,040 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:52:27] "GET /admin/dashboard HTTP/1.1" 200 -
2025-06-24 20:52:29,573 - app - ERROR - Error in admin_inventory: Could not build url for endpoint 'admin_orders'. Did you mean 'admin_redirect' instead?
2025-06-24 20:52:29,575 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:52:29] "[32mGET /admin/inventory HTTP/1.1[0m" 302 -
2025-06-24 20:52:29,589 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:52:29] "GET /admin/dashboard HTTP/1.1" 200 -
2025-06-24 20:52:29,669 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:52:29] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-24 20:52:29,671 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:52:29] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-24 20:52:29,739 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:52:29] "GET /admin/dashboard HTTP/1.1" 200 -
2025-06-24 20:52:34,138 - app - ERROR - Error in admin_inventory: Could not build url for endpoint 'admin_orders'. Did you mean 'admin_redirect' instead?
2025-06-24 20:52:34,140 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:52:34] "[32mGET /admin/inventory HTTP/1.1[0m" 302 -
2025-06-24 20:52:34,154 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:52:34] "GET /admin/dashboard HTTP/1.1" 200 -
2025-06-24 20:52:34,313 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:52:34] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-24 20:52:34,367 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:52:34] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-24 20:52:34,538 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:52:34] "GET /admin/dashboard HTTP/1.1" 200 -
2025-06-24 20:52:40,782 - app - ERROR - Error in admin_inventory: Could not build url for endpoint 'admin_orders'. Did you mean 'admin_redirect' instead?
2025-06-24 20:52:40,785 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:52:40] "[32mGET /admin/inventory HTTP/1.1[0m" 302 -
2025-06-24 20:52:40,799 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:52:40] "GET /admin/dashboard HTTP/1.1" 200 -
2025-06-24 20:52:40,872 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:52:40] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-24 20:52:40,873 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:52:40] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-24 20:52:40,951 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:52:40] "GET /admin/dashboard HTTP/1.1" 200 -
2025-06-24 20:52:47,679 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-06-24 20:52:47,680 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-24 20:52:47,682 - werkzeug - INFO -  * Restarting with stat
2025-06-24 20:52:48,399 - werkzeug - WARNING -  * Debugger is active!
2025-06-24 20:52:48,409 - werkzeug - INFO -  * Debugger PIN: 272-114-452
2025-06-24 20:52:55,368 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:52:55] "GET / HTTP/1.1" 200 -
2025-06-24 20:52:55,522 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:52:55] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-24 20:52:55,522 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:52:55] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-24 20:52:55,680 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:52:55] "GET / HTTP/1.1" 200 -
2025-06-24 20:53:01,418 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:53:01] "GET /admin/login HTTP/1.1" 200 -
2025-06-24 20:53:01,524 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:53:01] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-24 20:53:01,530 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:53:01] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-24 20:53:01,589 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:53:01] "GET /admin/login HTTP/1.1" 200 -
2025-06-24 20:53:02,573 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:53:02] "[32mPOST /admin/login HTTP/1.1[0m" 302 -
2025-06-24 20:53:02,650 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:53:02] "GET /admin/dashboard HTTP/1.1" 200 -
2025-06-24 20:53:02,714 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:53:02] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-24 20:53:02,714 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:53:02] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-24 20:53:02,739 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:53:02] "GET /admin/dashboard HTTP/1.1" 200 -
2025-06-24 20:53:03,894 - app - ERROR - Error in admin_inventory: Could not build url for endpoint 'admin_orders'. Did you mean 'admin_redirect' instead?
2025-06-24 20:53:03,897 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:53:03] "[32mGET /admin/inventory HTTP/1.1[0m" 302 -
2025-06-24 20:53:03,923 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:53:03] "GET /admin/dashboard HTTP/1.1" 200 -
2025-06-24 20:53:03,983 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:53:03] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-24 20:53:03,987 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:53:03] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-24 20:53:04,044 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:53:04] "GET /admin/dashboard HTTP/1.1" 200 -
2025-06-24 20:54:32,784 - werkzeug - INFO -  * Detected change in 'D:\\grocery_app\\app.py', reloading
2025-06-24 20:54:32,867 - werkzeug - INFO -  * Restarting with stat
2025-06-24 20:54:33,500 - werkzeug - WARNING -  * Debugger is active!
2025-06-24 20:54:33,512 - werkzeug - INFO -  * Debugger PIN: 272-114-452
2025-06-24 20:54:39,375 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-06-24 20:54:39,376 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-24 20:54:39,377 - werkzeug - INFO -  * Restarting with stat
2025-06-24 20:54:39,761 - werkzeug - WARNING -  * Debugger is active!
2025-06-24 20:54:39,766 - werkzeug - INFO -  * Debugger PIN: 272-114-452
2025-06-24 20:54:42,815 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:54:42] "GET / HTTP/1.1" 200 -
2025-06-24 20:54:42,978 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:54:42] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-24 20:54:42,993 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:54:42] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-24 20:54:43,111 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:54:43] "GET / HTTP/1.1" 200 -
2025-06-24 20:54:47,299 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:54:47] "GET /admin/login HTTP/1.1" 200 -
2025-06-24 20:54:47,395 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:54:47] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-24 20:54:47,411 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:54:47] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-24 20:54:47,495 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:54:47] "GET /admin/login HTTP/1.1" 200 -
2025-06-24 20:54:48,301 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:54:48] "[32mPOST /admin/login HTTP/1.1[0m" 302 -
2025-06-24 20:54:48,392 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:54:48] "GET /admin/dashboard HTTP/1.1" 200 -
2025-06-24 20:54:48,447 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:54:48] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-24 20:54:48,451 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:54:48] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-24 20:54:48,481 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:54:48] "GET /admin/dashboard HTTP/1.1" 200 -
2025-06-24 20:54:51,490 - app - ERROR - Error in admin_inventory: 'dict object' has no attribute 'image'
2025-06-24 20:54:51,492 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:54:51] "[32mGET /admin/inventory HTTP/1.1[0m" 302 -
2025-06-24 20:54:51,522 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:54:51] "GET /admin/dashboard HTTP/1.1" 200 -
2025-06-24 20:54:51,591 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:54:51] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-24 20:54:51,602 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:54:51] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-24 20:54:51,641 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:54:51] "GET /admin/dashboard HTTP/1.1" 200 -
2025-06-24 20:54:59,260 - app - ERROR - Error in shop route: 'dict object' has no attribute 'image'
2025-06-24 20:54:59,262 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:54:59] "[32mGET /shop HTTP/1.1[0m" 302 -
2025-06-24 20:54:59,291 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:54:59] "GET / HTTP/1.1" 200 -
2025-06-24 20:54:59,358 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:54:59] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-24 20:54:59,363 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:54:59] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-24 20:54:59,429 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:54:59] "GET / HTTP/1.1" 200 -
2025-06-24 20:55:03,175 - app - ERROR - Error in shop route: 'dict object' has no attribute 'image'
2025-06-24 20:55:03,176 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:55:03] "[32mGET /shop HTTP/1.1[0m" 302 -
2025-06-24 20:55:03,188 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:55:03] "GET / HTTP/1.1" 200 -
2025-06-24 20:55:03,265 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:55:03] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-24 20:55:03,272 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:55:03] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-24 20:55:03,334 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:55:03] "GET / HTTP/1.1" 200 -
2025-06-24 20:55:06,906 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:55:06] "GET /admin/login HTTP/1.1" 200 -
2025-06-24 20:55:06,973 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:55:06] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-24 20:55:06,974 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:55:06] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-24 20:55:07,042 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:55:07] "GET /admin/login HTTP/1.1" 200 -
2025-06-24 20:55:07,899 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:55:07] "[32mPOST /admin/login HTTP/1.1[0m" 302 -
2025-06-24 20:55:07,914 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:55:07] "GET /admin/dashboard HTTP/1.1" 200 -
2025-06-24 20:55:07,980 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:55:07] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-24 20:55:07,983 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:55:07] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-24 20:55:08,026 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:55:08] "GET /admin/dashboard HTTP/1.1" 200 -
2025-06-24 20:55:10,384 - app - ERROR - Error in admin_inventory: 'dict object' has no attribute 'image'
2025-06-24 20:55:10,387 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:55:10] "[32mGET /admin/inventory HTTP/1.1[0m" 302 -
2025-06-24 20:55:10,401 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:55:10] "GET /admin/dashboard HTTP/1.1" 200 -
2025-06-24 20:55:10,467 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:55:10] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-24 20:55:10,472 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:55:10] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-24 20:55:10,512 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:55:10] "GET /admin/dashboard HTTP/1.1" 200 -
2025-06-24 20:55:14,570 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:55:14] "[32mGET /debug/fix_orders HTTP/1.1[0m" 302 -
2025-06-24 20:55:14,582 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:55:14] "GET /admin/dashboard HTTP/1.1" 200 -
2025-06-24 20:55:14,680 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:55:14] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-24 20:55:14,689 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:55:14] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-24 20:55:14,717 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:55:14] "GET /admin/dashboard HTTP/1.1" 200 -
2025-06-24 20:56:39,901 - werkzeug - INFO -  * Detected change in 'D:\\grocery_app\\app.py', reloading
2025-06-24 20:56:39,986 - werkzeug - INFO -  * Restarting with stat
2025-06-24 20:56:40,603 - werkzeug - WARNING -  * Debugger is active!
2025-06-24 20:56:40,609 - werkzeug - INFO -  * Debugger PIN: 272-114-452
2025-06-24 20:56:57,780 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-06-24 20:56:57,782 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-24 20:56:57,784 - werkzeug - INFO -  * Restarting with stat
2025-06-24 20:56:58,308 - werkzeug - WARNING -  * Debugger is active!
2025-06-24 20:56:58,315 - werkzeug - INFO -  * Debugger PIN: 272-114-452
2025-06-24 20:57:01,171 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:57:01] "GET / HTTP/1.1" 200 -
2025-06-24 20:57:01,709 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:57:01] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-24 20:57:01,729 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:57:01] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-24 20:57:01,961 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:57:01] "GET / HTTP/1.1" 200 -
2025-06-24 20:57:07,253 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:57:07] "GET /admin/login HTTP/1.1" 200 -
2025-06-24 20:57:07,323 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:57:07] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-24 20:57:07,328 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:57:07] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-24 20:57:07,524 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:57:07] "GET /admin/login HTTP/1.1" 200 -
2025-06-24 20:57:08,643 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:57:08] "[32mPOST /admin/login HTTP/1.1[0m" 302 -
2025-06-24 20:57:08,725 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:57:08] "GET /admin/dashboard HTTP/1.1" 200 -
2025-06-24 20:57:08,798 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:57:08] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-24 20:57:08,798 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:57:08] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-24 20:57:08,831 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:57:08] "GET /admin/dashboard HTTP/1.1" 200 -
2025-06-24 20:57:12,120 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:57:12] "GET /admin/inventory HTTP/1.1" 200 -
2025-06-24 20:57:12,197 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:57:12] "[33mGET /static/img/default.jpg HTTP/1.1[0m" 404 -
2025-06-24 20:57:12,202 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:57:12] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-24 20:57:12,208 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:57:12] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-24 20:57:12,212 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:57:12] "GET /static/css/admin.css HTTP/1.1" 200 -
2025-06-24 20:57:12,240 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:57:12] "[33mGET /static/img/default.jpg HTTP/1.1[0m" 404 -
2025-06-24 20:57:12,271 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:57:12] "GET /admin/inventory HTTP/1.1" 200 -
2025-06-24 20:57:27,252 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:57:27] "GET /admin/add_item HTTP/1.1" 200 -
2025-06-24 20:57:27,321 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:57:27] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-24 20:57:27,321 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:57:27] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-24 20:57:27,323 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:57:27] "[36mGET /static/css/admin.css HTTP/1.1[0m" 304 -
2025-06-24 20:57:27,364 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:57:27] "GET /admin/add_item HTTP/1.1" 200 -
2025-06-24 20:57:35,235 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:57:35] "GET /admin/inventory HTTP/1.1" 200 -
2025-06-24 20:57:35,305 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:57:35] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-24 20:57:35,317 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:57:35] "[36mGET /static/css/admin.css HTTP/1.1[0m" 304 -
2025-06-24 20:57:35,320 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:57:35] "[33mGET /static/img/default.jpg HTTP/1.1[0m" 404 -
2025-06-24 20:57:35,325 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:57:35] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-24 20:57:35,514 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:57:35] "GET /admin/inventory HTTP/1.1" 200 -
2025-06-24 20:57:37,668 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:57:37] "[32mPOST /admin/update_item/Mango HTTP/1.1[0m" 302 -
2025-06-24 20:57:37,984 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:57:37] "GET /admin/inventory HTTP/1.1" 200 -
2025-06-24 20:57:38,681 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:57:38] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-24 20:57:38,734 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:57:38] "[36mGET /static/css/admin.css HTTP/1.1[0m" 304 -
2025-06-24 20:57:38,792 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:57:38] "[33mGET /static/img/default.jpg HTTP/1.1[0m" 404 -
2025-06-24 20:57:38,852 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:57:38] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-24 20:57:41,538 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:57:41] "[32mPOST /admin/update_item/Apple HTTP/1.1[0m" 302 -
2025-06-24 20:57:41,797 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:57:41] "GET /admin/inventory HTTP/1.1" 200 -
2025-06-24 20:57:42,167 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:57:42] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-24 20:57:42,286 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:57:42] "[36mGET /static/css/admin.css HTTP/1.1[0m" 304 -
2025-06-24 20:57:42,325 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:57:42] "[33mGET /static/img/default.jpg HTTP/1.1[0m" 404 -
2025-06-24 20:57:42,374 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:57:42] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-24 20:57:44,602 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:57:44] "GET /admin/inventory HTTP/1.1" 200 -
2025-06-24 20:57:44,630 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:57:44] "GET /admin/inventory HTTP/1.1" 200 -
2025-06-24 20:57:45,536 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:57:45] "GET /admin/edit_item/Mango HTTP/1.1" 200 -
2025-06-24 20:57:45,588 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:57:45] "[33mGET /static/img/default.jpg HTTP/1.1[0m" 404 -
2025-06-24 20:57:45,594 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:57:45] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-24 20:57:45,599 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:57:45] "[36mGET /static/css/admin.css HTTP/1.1[0m" 304 -
2025-06-24 20:57:45,608 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:57:45] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-24 20:57:45,666 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:57:45] "GET /admin/edit_item/Mango HTTP/1.1" 200 -
2025-06-24 20:57:48,457 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:57:48] "GET /admin/inventory HTTP/1.1" 200 -
2025-06-24 20:57:48,517 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:57:48] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-24 20:57:48,519 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:57:48] "[33mGET /static/img/default.jpg HTTP/1.1[0m" 404 -
2025-06-24 20:57:48,520 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:57:48] "[36mGET /static/css/admin.css HTTP/1.1[0m" 304 -
2025-06-24 20:57:48,524 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:57:48] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-24 20:57:48,575 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:57:48] "[33mGET /static/img/default.jpg HTTP/1.1[0m" 404 -
2025-06-24 20:57:48,676 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:57:48] "GET /admin/inventory HTTP/1.1" 200 -
2025-06-24 20:57:49,683 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:57:49] "[32mPOST /admin/update_item/Banana HTTP/1.1[0m" 302 -
2025-06-24 20:57:49,722 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:57:49] "GET /admin/inventory HTTP/1.1" 200 -
2025-06-24 20:57:49,803 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:57:49] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-24 20:57:49,804 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:57:49] "[36mGET /static/css/admin.css HTTP/1.1[0m" 304 -
2025-06-24 20:57:49,807 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:57:49] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-24 20:57:49,810 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:57:49] "[33mGET /static/img/default.jpg HTTP/1.1[0m" 404 -
2025-06-24 20:57:49,871 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:57:49] "GET /admin/inventory HTTP/1.1" 200 -
2025-06-24 20:57:50,756 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:57:50] "GET / HTTP/1.1" 200 -
2025-06-24 20:57:50,823 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:57:50] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-24 20:57:50,832 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:57:50] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-24 20:57:50,898 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:57:50] "GET / HTTP/1.1" 200 -
2025-06-24 20:57:53,173 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:57:53] "GET / HTTP/1.1" 200 -
2025-06-24 20:57:53,210 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:57:53] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-24 20:57:53,211 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:57:53] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-24 20:57:53,263 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:57:53] "GET / HTTP/1.1" 200 -
2025-06-24 20:57:58,908 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:57:58] "GET /admin/login HTTP/1.1" 200 -
2025-06-24 20:57:58,997 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:57:58] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-24 20:57:58,999 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:57:58] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-24 20:57:59,055 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:57:59] "GET /admin/login HTTP/1.1" 200 -
2025-06-24 20:57:59,838 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:57:59] "[32mPOST /admin/login HTTP/1.1[0m" 302 -
2025-06-24 20:57:59,859 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:57:59] "GET /admin/dashboard HTTP/1.1" 200 -
2025-06-24 20:57:59,954 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:57:59] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-24 20:57:59,955 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:57:59] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-24 20:57:59,977 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:57:59] "GET /admin/dashboard HTTP/1.1" 200 -
2025-06-24 20:58:05,330 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:58:05] "GET /admin/inventory HTTP/1.1" 200 -
2025-06-24 20:58:05,405 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:58:05] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-24 20:58:05,412 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:58:05] "[36mGET /static/css/admin.css HTTP/1.1[0m" 304 -
2025-06-24 20:58:05,428 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:58:05] "[33mGET /static/img/default.jpg HTTP/1.1[0m" 404 -
2025-06-24 20:58:05,441 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:58:05] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-24 20:58:05,471 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:58:05] "[33mGET /static/img/default.jpg HTTP/1.1[0m" 404 -
2025-06-24 20:58:05,542 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:58:05] "GET /admin/inventory HTTP/1.1" 200 -
2025-06-24 20:58:11,122 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:58:11] "GET /admin/dashboard HTTP/1.1" 200 -
2025-06-24 20:58:11,166 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:58:11] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-24 20:58:11,173 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:58:11] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-24 20:58:11,242 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:58:11] "GET /admin/dashboard HTTP/1.1" 200 -
2025-06-24 20:58:12,696 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:58:12] "GET / HTTP/1.1" 200 -
2025-06-24 20:58:12,766 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:58:12] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-24 20:58:12,768 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:58:12] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-24 20:58:12,823 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:58:12] "GET / HTTP/1.1" 200 -
2025-06-24 20:59:24,991 - werkzeug - INFO -  * Detected change in 'D:\\grocery_app\\app.py', reloading
2025-06-24 20:59:25,088 - werkzeug - INFO -  * Restarting with stat
2025-06-24 20:59:25,704 - werkzeug - WARNING -  * Debugger is active!
2025-06-24 20:59:25,714 - werkzeug - INFO -  * Debugger PIN: 272-114-452
2025-06-24 20:59:41,408 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:59:41] "GET / HTTP/1.1" 200 -
2025-06-24 20:59:41,584 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:59:41] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-24 20:59:41,593 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:59:41] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-24 20:59:41,669 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:59:41] "GET / HTTP/1.1" 200 -
2025-06-24 20:59:45,258 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:59:45] "GET / HTTP/1.1" 200 -
2025-06-24 20:59:45,298 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:59:45] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-24 20:59:45,303 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:59:45] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-24 20:59:45,395 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:59:45] "GET / HTTP/1.1" 200 -
2025-06-24 20:59:50,649 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-06-24 20:59:50,649 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-24 20:59:50,651 - werkzeug - INFO -  * Restarting with stat
2025-06-24 20:59:51,330 - werkzeug - WARNING -  * Debugger is active!
2025-06-24 20:59:51,342 - werkzeug - INFO -  * Debugger PIN: 272-114-452
2025-06-24 20:59:53,699 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:59:53] "GET / HTTP/1.1" 200 -
2025-06-24 20:59:53,875 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:59:53] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-24 20:59:53,879 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:59:53] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-24 20:59:54,069 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 20:59:54] "GET / HTTP/1.1" 200 -
2025-06-24 21:01:10,089 - werkzeug - INFO -  * Detected change in 'D:\\grocery_app\\app.py', reloading
2025-06-24 21:01:10,242 - werkzeug - INFO -  * Restarting with stat
2025-06-24 21:01:10,738 - werkzeug - WARNING -  * Debugger is active!
2025-06-24 21:01:10,745 - werkzeug - INFO -  * Debugger PIN: 272-114-452
2025-06-24 21:01:22,481 - app - DEBUG - Loaded 11 items from grocery data
2025-06-24 21:01:22,483 - app - DEBUG - Selected 4 featured items
2025-06-24 21:01:22,579 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:01:22] "GET / HTTP/1.1" 200 -
2025-06-24 21:01:22,639 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:01:22] "[33mGET /static/img/default.jpg HTTP/1.1[0m" 404 -
2025-06-24 21:01:22,666 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:01:22] "[33mGET /static/img/default.jpg HTTP/1.1[0m" 404 -
2025-06-24 21:01:22,791 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:01:22] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-24 21:01:22,803 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:01:22] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-24 21:01:22,897 - app - DEBUG - Loaded 11 items from grocery data
2025-06-24 21:01:22,898 - app - DEBUG - Selected 4 featured items
2025-06-24 21:01:22,899 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:01:22] "GET / HTTP/1.1" 200 -
2025-06-24 21:01:35,648 - app - DEBUG - Loaded 11 items from grocery data
2025-06-24 21:01:35,649 - app - DEBUG - Selected 4 featured items
2025-06-24 21:01:35,652 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:01:35] "GET / HTTP/1.1" 200 -
2025-06-24 21:01:35,732 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:01:35] "[33mGET /static/img/default.jpg HTTP/1.1[0m" 404 -
2025-06-24 21:01:35,740 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:01:35] "GET /static/css/style.css HTTP/1.1" 200 -
2025-06-24 21:01:35,774 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:01:35] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-24 21:01:35,793 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:01:35] "[33mGET /static/img/default.jpg HTTP/1.1[0m" 404 -
2025-06-24 21:01:35,823 - app - DEBUG - Loaded 11 items from grocery data
2025-06-24 21:01:35,824 - app - DEBUG - Selected 4 featured items
2025-06-24 21:01:35,826 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:01:35] "GET / HTTP/1.1" 200 -
2025-06-24 21:01:58,546 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:01:58] "[32mPOST /add_to_cart/Milk HTTP/1.1[0m" 302 -
2025-06-24 21:01:58,615 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:01:58] "GET /shop HTTP/1.1" 200 -
2025-06-24 21:01:58,678 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:01:58] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-24 21:01:58,686 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:01:58] "[33mGET /static/img/default.jpg HTTP/1.1[0m" 404 -
2025-06-24 21:01:58,687 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:01:58] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-24 21:01:58,767 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:01:58] "GET /shop HTTP/1.1" 200 -
2025-06-24 21:02:07,477 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:02:07] "GET /cart HTTP/1.1" 200 -
2025-06-24 21:02:07,546 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:02:07] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-24 21:02:07,556 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:02:07] "[33mGET /static/img/default.jpg HTTP/1.1[0m" 404 -
2025-06-24 21:02:07,567 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:02:07] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-24 21:02:07,613 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:02:07] "GET /cart HTTP/1.1" 200 -
2025-06-24 21:02:09,490 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:02:09] "GET /checkout HTTP/1.1" 200 -
2025-06-24 21:02:09,613 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:02:09] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-24 21:02:09,626 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:02:09] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-24 21:02:09,876 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:02:09] "GET /checkout HTTP/1.1" 200 -
2025-06-24 21:02:12,318 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:02:12] "[32mPOST /checkout HTTP/1.1[0m" 302 -
2025-06-24 21:02:12,423 - app - ERROR - Error in order_confirmation: 'builtin_function_or_method' object is not iterable
2025-06-24 21:02:12,427 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:02:12] "[32mGET /order_confirmation/d9bd3bc8-1642-44e4-950b-a528fd8e0e0a HTTP/1.1[0m" 302 -
2025-06-24 21:02:12,476 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:02:12] "GET /shop HTTP/1.1" 200 -
2025-06-24 21:02:12,530 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:02:12] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-24 21:02:12,547 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:02:12] "[33mGET /static/img/default.jpg HTTP/1.1[0m" 404 -
2025-06-24 21:02:12,558 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:02:12] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-24 21:02:12,645 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:02:12] "GET /shop HTTP/1.1" 200 -
2025-06-24 21:02:15,507 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:02:15] "GET /admin/login HTTP/1.1" 200 -
2025-06-24 21:02:15,563 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:02:15] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-24 21:02:15,564 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:02:15] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-24 21:02:15,608 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:02:15] "GET /admin/login HTTP/1.1" 200 -
2025-06-24 21:02:16,275 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:02:16] "[32mPOST /admin/login HTTP/1.1[0m" 302 -
2025-06-24 21:02:16,359 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:02:16] "GET /admin/dashboard HTTP/1.1" 200 -
2025-06-24 21:02:16,458 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:02:16] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-24 21:02:16,459 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:02:16] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-24 21:02:16,518 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:02:16] "GET /admin/dashboard HTTP/1.1" 200 -
2025-06-24 21:02:22,553 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:02:22] "[32mPOST /admin/update_order/d9bd3bc8-1642-44e4-950b-a528fd8e0e0a HTTP/1.1[0m" 302 -
2025-06-24 21:02:22,610 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:02:22] "GET /admin/dashboard HTTP/1.1" 200 -
2025-06-24 21:02:22,685 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:02:22] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-24 21:02:22,686 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:02:22] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-24 21:02:22,778 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:02:22] "GET /admin/dashboard HTTP/1.1" 200 -
2025-06-24 21:02:32,240 - app - DEBUG - Loaded 11 items from grocery data
2025-06-24 21:02:32,240 - app - DEBUG - Selected 4 featured items
2025-06-24 21:02:32,244 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:02:32] "GET /home HTTP/1.1" 200 -
2025-06-24 21:02:32,289 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:02:32] "[33mGET /static/img/default.jpg HTTP/1.1[0m" 404 -
2025-06-24 21:02:32,293 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:02:32] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-24 21:02:32,319 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:02:32] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-24 21:02:32,359 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:02:32] "[33mGET /static/img/default.jpg HTTP/1.1[0m" 404 -
2025-06-24 21:02:32,371 - app - DEBUG - Loaded 11 items from grocery data
2025-06-24 21:02:32,372 - app - DEBUG - Selected 4 featured items
2025-06-24 21:02:32,377 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:02:32] "GET /home HTTP/1.1" 200 -
2025-06-24 21:02:55,879 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:02:55] "[32mPOST /add_to_cart/Milk HTTP/1.1[0m" 302 -
2025-06-24 21:02:55,896 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:02:55] "GET /shop HTTP/1.1" 200 -
2025-06-24 21:02:55,966 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:02:55] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-24 21:02:55,970 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:02:55] "[33mGET /static/img/default.jpg HTTP/1.1[0m" 404 -
2025-06-24 21:02:55,973 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:02:55] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-24 21:02:56,044 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:02:56] "GET /shop HTTP/1.1" 200 -
2025-06-24 21:02:59,658 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:02:59] "GET /cart HTTP/1.1" 200 -
2025-06-24 21:02:59,733 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:02:59] "[33mGET /static/img/default.jpg HTTP/1.1[0m" 404 -
2025-06-24 21:02:59,734 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:02:59] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-24 21:02:59,735 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:02:59] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-24 21:02:59,827 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:02:59] "GET /cart HTTP/1.1" 200 -
2025-06-24 21:03:02,441 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:03:02] "[32mGET /remove_from_cart/Milk HTTP/1.1[0m" 302 -
2025-06-24 21:03:02,456 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:03:02] "GET /cart HTTP/1.1" 200 -
2025-06-24 21:03:02,534 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:03:02] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-24 21:03:02,545 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:03:02] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-24 21:03:02,603 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:03:02] "GET /cart HTTP/1.1" 200 -
2025-06-24 21:03:03,167 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:03:03] "GET /admin/login HTTP/1.1" 200 -
2025-06-24 21:03:03,234 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:03:03] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-24 21:03:03,239 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:03:03] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-24 21:03:03,308 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:03:03] "GET /admin/login HTTP/1.1" 200 -
2025-06-24 21:03:04,090 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:03:04] "[32mPOST /admin/login HTTP/1.1[0m" 302 -
2025-06-24 21:03:04,103 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:03:04] "GET /admin/dashboard HTTP/1.1" 200 -
2025-06-24 21:03:04,199 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:03:04] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-24 21:03:04,203 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:03:04] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-24 21:03:04,235 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:03:04] "GET /admin/dashboard HTTP/1.1" 200 -
2025-06-24 21:03:08,499 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:03:08] "GET /admin/dashboard HTTP/1.1" 200 -
2025-06-24 21:03:08,560 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:03:08] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-24 21:03:08,569 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:03:08] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-24 21:03:08,632 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:03:08] "GET /admin/dashboard HTTP/1.1" 200 -
2025-06-24 21:03:16,103 - app - DEBUG - Loaded 11 items from grocery data
2025-06-24 21:03:16,104 - app - DEBUG - Selected 4 featured items
2025-06-24 21:03:16,107 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:03:16] "GET /home HTTP/1.1" 200 -
2025-06-24 21:03:16,204 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:03:16] "[33mGET /static/img/default.jpg HTTP/1.1[0m" 404 -
2025-06-24 21:03:16,205 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:03:16] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-24 21:03:16,209 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:03:16] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-24 21:03:16,345 - app - DEBUG - Loaded 11 items from grocery data
2025-06-24 21:03:16,348 - app - DEBUG - Selected 4 featured items
2025-06-24 21:03:16,352 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:03:16] "GET /home HTTP/1.1" 200 -
2025-06-24 21:03:18,980 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:03:18] "[32mPOST /add_to_cart/Cookies HTTP/1.1[0m" 302 -
2025-06-24 21:03:18,994 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:03:18] "GET /shop HTTP/1.1" 200 -
2025-06-24 21:03:19,087 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:03:19] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-24 21:03:19,097 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:03:19] "[33mGET /static/img/default.jpg HTTP/1.1[0m" 404 -
2025-06-24 21:03:19,109 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:03:19] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-24 21:03:19,124 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:03:19] "[33mGET /static/img/default.jpg HTTP/1.1[0m" 404 -
2025-06-24 21:03:19,222 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:03:19] "GET /shop HTTP/1.1" 200 -
2025-06-24 21:03:21,856 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:03:21] "GET /cart HTTP/1.1" 200 -
2025-06-24 21:03:21,958 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:03:21] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-24 21:03:21,968 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:03:21] "[33mGET /static/img/default.jpg HTTP/1.1[0m" 404 -
2025-06-24 21:03:21,974 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:03:21] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-24 21:03:22,094 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:03:22] "GET /cart HTTP/1.1" 200 -
2025-06-24 21:03:23,986 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:03:23] "GET /checkout HTTP/1.1" 200 -
2025-06-24 21:03:24,080 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:03:24] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-24 21:03:24,095 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:03:24] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-24 21:03:24,177 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:03:24] "GET /checkout HTTP/1.1" 200 -
2025-06-24 21:03:27,332 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:03:27] "[32mPOST /checkout HTTP/1.1[0m" 302 -
2025-06-24 21:03:27,390 - app - ERROR - Error in order_confirmation: 'builtin_function_or_method' object is not iterable
2025-06-24 21:03:27,393 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:03:27] "[32mGET /order_confirmation/32c2de2d-c4aa-4449-b0c8-01204479973e HTTP/1.1[0m" 302 -
2025-06-24 21:03:27,469 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:03:27] "GET /shop HTTP/1.1" 200 -
2025-06-24 21:03:27,531 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:03:27] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-24 21:03:27,533 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:03:27] "[33mGET /static/img/default.jpg HTTP/1.1[0m" 404 -
2025-06-24 21:03:27,553 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:03:27] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-24 21:03:27,638 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:03:27] "GET /shop HTTP/1.1" 200 -
2025-06-24 21:05:02,309 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:05:02] "GET /shop HTTP/1.1" 200 -
2025-06-24 21:05:02,372 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:05:02] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-24 21:05:02,381 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:05:02] "[33mGET /static/img/default.jpg HTTP/1.1[0m" 404 -
2025-06-24 21:05:02,388 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:05:02] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-24 21:05:02,452 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:05:02] "GET /shop HTTP/1.1" 200 -
2025-06-24 21:05:06,939 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:05:06] "[32mPOST /add_to_cart/Mango HTTP/1.1[0m" 302 -
2025-06-24 21:05:07,023 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:05:07] "GET /shop HTTP/1.1" 200 -
2025-06-24 21:05:07,133 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:05:07] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-24 21:05:07,173 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:05:07] "[33mGET /static/img/default.jpg HTTP/1.1[0m" 404 -
2025-06-24 21:05:07,229 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:05:07] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-24 21:05:07,339 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:05:07] "GET /shop HTTP/1.1" 200 -
2025-06-24 21:05:09,673 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:05:09] "GET /cart HTTP/1.1" 200 -
2025-06-24 21:05:09,737 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:05:09] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-24 21:05:09,753 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:05:09] "[33mGET /static/img/default.jpg HTTP/1.1[0m" 404 -
2025-06-24 21:05:09,754 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:05:09] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-24 21:05:09,821 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:05:09] "GET /cart HTTP/1.1" 200 -
2025-06-24 21:05:11,405 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:05:11] "GET /checkout HTTP/1.1" 200 -
2025-06-24 21:05:11,458 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:05:11] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-24 21:05:11,462 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:05:11] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-24 21:05:11,509 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:05:11] "GET /checkout HTTP/1.1" 200 -
2025-06-24 21:05:14,026 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:05:14] "[32mPOST /checkout HTTP/1.1[0m" 302 -
2025-06-24 21:05:14,106 - app - ERROR - Error in order_confirmation: 'builtin_function_or_method' object is not iterable
2025-06-24 21:05:14,136 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:05:14] "[32mGET /order_confirmation/ce7da291-4c1e-46fe-8c5e-70c43528773a HTTP/1.1[0m" 302 -
2025-06-24 21:05:14,208 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:05:14] "GET /shop HTTP/1.1" 200 -
2025-06-24 21:05:14,268 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:05:14] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-24 21:05:14,272 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:05:14] "[33mGET /static/img/default.jpg HTTP/1.1[0m" 404 -
2025-06-24 21:05:14,274 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:05:14] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-24 21:05:14,349 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:05:14] "GET /shop HTTP/1.1" 200 -
2025-06-24 21:05:17,528 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:05:17] "GET /admin/login HTTP/1.1" 200 -
2025-06-24 21:05:17,600 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:05:17] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-24 21:05:17,618 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:05:17] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-24 21:05:17,695 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:05:17] "GET /admin/login HTTP/1.1" 200 -
2025-06-24 21:05:18,468 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:05:18] "[32mPOST /admin/login HTTP/1.1[0m" 302 -
2025-06-24 21:05:18,485 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:05:18] "GET /admin/dashboard HTTP/1.1" 200 -
2025-06-24 21:05:18,570 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:05:18] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-24 21:05:18,575 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:05:18] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-24 21:05:18,601 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:05:18] "GET /admin/dashboard HTTP/1.1" 200 -
2025-06-24 21:06:40,604 - werkzeug - INFO -  * Detected change in 'D:\\grocery_app\\app.py', reloading
2025-06-24 21:06:40,678 - werkzeug - INFO -  * Restarting with stat
2025-06-24 21:06:41,531 - werkzeug - WARNING -  * Debugger is active!
2025-06-24 21:06:41,539 - werkzeug - INFO -  * Debugger PIN: 272-114-452
2025-06-24 21:06:49,608 - app - DEBUG - Loaded 11 items from grocery data
2025-06-24 21:06:49,649 - app - DEBUG - Selected 4 featured items
2025-06-24 21:06:49,799 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:06:49] "GET /home HTTP/1.1" 200 -
2025-06-24 21:06:50,016 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:06:50] "[33mGET /static/img/default.jpg HTTP/1.1[0m" 404 -
2025-06-24 21:06:50,247 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:06:50] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-24 21:06:50,255 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:06:50] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-24 21:06:50,624 - app - DEBUG - Loaded 11 items from grocery data
2025-06-24 21:06:50,625 - app - DEBUG - Selected 4 featured items
2025-06-24 21:06:50,628 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:06:50] "GET /home HTTP/1.1" 200 -
2025-06-24 21:06:52,305 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:06:52] "[32mPOST /add_to_cart/Cheese HTTP/1.1[0m" 302 -
2025-06-24 21:06:52,364 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:06:52] "GET /shop HTTP/1.1" 200 -
2025-06-24 21:06:52,409 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:06:52] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-24 21:06:52,410 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:06:52] "[33mGET /static/img/default.jpg HTTP/1.1[0m" 404 -
2025-06-24 21:06:52,423 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:06:52] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-24 21:06:52,446 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:06:52] "[33mGET /static/img/default.jpg HTTP/1.1[0m" 404 -
2025-06-24 21:06:52,500 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:06:52] "GET /shop HTTP/1.1" 200 -
2025-06-24 21:06:53,498 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:06:53] "GET /cart HTTP/1.1" 200 -
2025-06-24 21:06:53,577 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:06:53] "[33mGET /static/img/default.jpg HTTP/1.1[0m" 404 -
2025-06-24 21:06:53,581 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:06:53] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-24 21:06:53,591 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:06:53] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-24 21:06:53,663 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:06:53] "GET /cart HTTP/1.1" 200 -
2025-06-24 21:06:54,748 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:06:54] "GET /checkout HTTP/1.1" 200 -
2025-06-24 21:06:54,791 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:06:54] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-24 21:06:54,794 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:06:54] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-24 21:06:54,818 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:06:54] "GET /checkout HTTP/1.1" 200 -
2025-06-24 21:06:57,533 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:06:57] "POST /checkout HTTP/1.1" 200 -
2025-06-24 21:06:57,603 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:06:57] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-24 21:06:57,604 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:06:57] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-24 21:06:58,073 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:06:58] "[32mGET /checkout HTTP/1.1[0m" 302 -
2025-06-24 21:06:58,114 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:06:58] "GET /shop HTTP/1.1" 200 -
2025-06-24 21:07:38,295 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:07:38] "GET /shop HTTP/1.1" 200 -
2025-06-24 21:07:38,336 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:07:38] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-24 21:07:38,343 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:07:38] "[33mGET /static/img/default.jpg HTTP/1.1[0m" 404 -
2025-06-24 21:07:38,347 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:07:38] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-24 21:07:38,383 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:07:38] "[33mGET /static/img/default.jpg HTTP/1.1[0m" 404 -
2025-06-24 21:07:38,422 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:07:38] "GET /shop HTTP/1.1" 200 -
2025-06-24 21:07:43,831 - app - DEBUG - Loaded 11 items from grocery data
2025-06-24 21:07:43,832 - app - DEBUG - Selected 4 featured items
2025-06-24 21:07:43,834 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:07:43] "GET /home HTTP/1.1" 200 -
2025-06-24 21:07:43,934 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:07:43] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-24 21:07:43,939 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:07:43] "[33mGET /static/img/default.jpg HTTP/1.1[0m" 404 -
2025-06-24 21:07:43,979 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:07:43] "[36mGET /static/js/script.js HTTP/1.1[0m" 304 -
2025-06-24 21:07:44,010 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:07:44] "[33mGET /static/img/default.jpg HTTP/1.1[0m" 404 -
2025-06-24 21:07:44,065 - app - DEBUG - Loaded 11 items from grocery data
2025-06-24 21:07:44,065 - app - DEBUG - Selected 4 featured items
2025-06-24 21:07:44,072 - werkzeug - INFO - 127.0.0.1 - - [24/Jun/2025 21:07:44] "GET /home HTTP/1.1" 200 -
2025-06-24 21:11:01,982 - werkzeug - INFO -  * Detected change in 'D:\\grocery_app\\app.py', reloading
2025-06-24 21:11:02,152 - werkzeug - INFO -  * Restarting with stat
2025-06-24 21:11:02,696 - werkzeug - WARNING -  * Debugger is active!
2025-06-24 21:11:02,702 - werkzeug - INFO -  * Debugger PIN: 272-114-452
